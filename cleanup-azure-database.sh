#!/bin/bash

# Azure SQL Database Cleanup Script
# Run this to clean up existing Azure SQL database before fresh setup

echo "=== SmartBoat Azure SQL Database Cleanup ==="
echo "This script will clean up your existing Azure SQL database."
echo ""

# Get database connection details
read -p "Enter SQL Server name (e.g., smartboat-prod-sql): " SQL_SERVER_NAME
read -p "Enter database name (e.g., smartboat): " SQL_DATABASE_NAME  
read -p "Enter admin username: " SQL_ADMIN_USER
read -s -p "Enter admin password: " SQL_ADMIN_PASSWORD
echo ""

SERVER_FQDN="${SQL_SERVER_NAME}.database.windows.net"

echo "Connecting to: $SERVER_FQDN"
echo "Database: $SQL_DATABASE_NAME"
echo ""

# Check if sqlcmd is available
if ! command -v sqlcmd &> /dev/null; then
    echo "❌ sqlcmd not found. Please install SQL Server command line tools:"
    echo ""
    echo "On macOS:"
    echo "brew install mssql-tools"
    echo ""
    echo "On Linux:"
    echo "curl https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -"
    echo "curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list | sudo tee /etc/apt/sources.list.d/msprod.list"
    echo "sudo apt-get update && sudo apt-get install -y mssql-tools"
    echo ""
    exit 1
fi

echo "✅ sqlcmd found, proceeding with database cleanup..."

# Function to execute SQL script
execute_sql() {
    local sql_command="$1"
    local description="$2"
    
    echo "⚠️  $description..."
    
    sqlcmd -S "$SERVER_FQDN" -d "$SQL_DATABASE_NAME" -U "$SQL_ADMIN_USER" -P "$SQL_ADMIN_PASSWORD" -Q "$sql_command" -b
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed: $description"
        return 1
    else
        echo "✅ Success: $description"
        return 0
    fi
}

# Check if database exists
echo "🔍 Checking if database exists..."
DB_CHECK=$(sqlcmd -S "$SERVER_FQDN" -d master -U "$SQL_ADMIN_USER" -P "$SQL_ADMIN_PASSWORD" -Q "SELECT name FROM sys.databases WHERE name = '$SQL_DATABASE_NAME'" -h-1 | grep -c "$SQL_DATABASE_NAME")

if [ "$DB_CHECK" -eq 0 ]; then
    echo "ℹ️  Database '$SQL_DATABASE_NAME' does not exist. Nothing to clean up."
    exit 0
fi

echo "🗑️  Starting database cleanup..."
echo ""

# Confirmation prompt
read -p "⚠️  WARNING: This will delete ALL data in database '$SQL_DATABASE_NAME'. Continue? (y/N): " CONFIRM
if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
    echo "❌ Cleanup cancelled by user."
    exit 1
fi

echo ""
echo "🔥 Dropping tables in dependency order..."

# Drop tables in reverse dependency order to avoid constraint violations
execute_sql "IF OBJECT_ID('UserRolePermissions', 'U') IS NOT NULL DROP TABLE UserRolePermissions;" "Dropping UserRolePermissions"
execute_sql "IF OBJECT_ID('RolePermission', 'U') IS NOT NULL DROP TABLE RolePermission;" "Dropping RolePermission"
execute_sql "IF OBJECT_ID('EntityPermissions', 'U') IS NOT NULL DROP TABLE EntityPermissions;" "Dropping EntityPermissions"
execute_sql "IF OBJECT_ID('SubscriptionFeature', 'U') IS NOT NULL DROP TABLE SubscriptionFeature;" "Dropping SubscriptionFeature"
execute_sql "IF OBJECT_ID('NotificationPreference', 'U') IS NOT NULL DROP TABLE NotificationPreference;" "Dropping NotificationPreference"
execute_sql "IF OBJECT_ID('AlertDelivery', 'U') IS NOT NULL DROP TABLE AlertDelivery;" "Dropping AlertDelivery"

# Drop dependent entity tables
execute_sql "IF OBJECT_ID('VesselPathPoints', 'U') IS NOT NULL DROP TABLE VesselPathPoints;" "Dropping VesselPathPoints"
execute_sql "IF OBJECT_ID('SensorDataPoints', 'U') IS NOT NULL DROP TABLE SensorDataPoints;" "Dropping SensorDataPoints"
execute_sql "IF OBJECT_ID('Sensors', 'U') IS NOT NULL DROP TABLE Sensors;" "Dropping Sensors"
execute_sql "IF OBJECT_ID('Alert', 'U') IS NOT NULL DROP TABLE Alert;" "Dropping Alert"
execute_sql "IF OBJECT_ID('Notification', 'U') IS NOT NULL DROP TABLE Notification;" "Dropping Notification"
execute_sql "IF OBJECT_ID('AuditLog', 'U') IS NOT NULL DROP TABLE AuditLog;" "Dropping AuditLog"
execute_sql "IF OBJECT_ID('Feedback', 'U') IS NOT NULL DROP TABLE Feedback;" "Dropping Feedback"
execute_sql "IF OBJECT_ID('SupportRequests', 'U') IS NOT NULL DROP TABLE SupportRequests;" "Dropping SupportRequests"
execute_sql "IF OBJECT_ID('Reports', 'U') IS NOT NULL DROP TABLE Reports;" "Dropping Reports"

# Drop main entity tables
execute_sql "IF OBJECT_ID('Vessels', 'U') IS NOT NULL DROP TABLE Vessels;" "Dropping Vessels"
execute_sql "IF OBJECT_ID('Subscription', 'U') IS NOT NULL DROP TABLE Subscription;" "Dropping Subscription"
execute_sql "IF OBJECT_ID('Customer', 'U') IS NOT NULL DROP TABLE Customer;" "Dropping Customer"
execute_sql "IF OBJECT_ID('Users', 'U') IS NOT NULL DROP TABLE Users;" "Dropping Users"
execute_sql "IF OBJECT_ID('Company', 'U') IS NOT NULL DROP TABLE Company;" "Dropping Company"

# Drop authorization tables
execute_sql "IF OBJECT_ID('Permissions', 'U') IS NOT NULL DROP TABLE Permissions;" "Dropping Permissions"
execute_sql "IF OBJECT_ID('Role', 'U') IS NOT NULL DROP TABLE Role;" "Dropping Role"

echo ""
echo "🔄 Checking for any remaining tables..."

# Check for remaining tables
REMAINING_CHECK=$(sqlcmd -S "$SERVER_FQDN" -d "$SQL_DATABASE_NAME" -U "$SQL_ADMIN_USER" -P "$SQL_ADMIN_PASSWORD" -Q "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'" -h-1 | tr -d ' ')

if [ "$REMAINING_CHECK" -gt 0 ]; then
    echo "⚠️  Warning: $REMAINING_CHECK table(s) still exist. Listing them..."
    sqlcmd -S "$SERVER_FQDN" -d "$SQL_DATABASE_NAME" -U "$SQL_ADMIN_USER" -P "$SQL_ADMIN_PASSWORD" -Q "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'" -h-1
    echo ""
    read -p "Attempt to drop remaining tables? (y/N): " DROP_REMAINING
    if [[ $DROP_REMAINING =~ ^[Yy]$ ]]; then
        execute_sql "DECLARE @sql NVARCHAR(MAX) = ''; SELECT @sql = @sql + 'DROP TABLE [' + TABLE_SCHEMA + '].[' + TABLE_NAME + '];' FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'; EXEC sp_executesql @sql;" "Dropping all remaining tables"
    fi
fi

echo ""
echo "✨ Azure SQL Database cleanup completed!"
echo ""
echo "📋 Summary:"
echo "- Database '$SQL_DATABASE_NAME' has been cleaned"
echo "- All tables have been dropped"
echo "- Ready for fresh setup"
echo ""
echo "💡 Next steps:"
echo "   Run: ./setup-database-manually.sh"
echo ""