#!/bin/bash
# Azure SQL Database Setup Script

# Parameters passed from deployment script
SERVER_NAME="$1"
DATABASE_NAME="$2"
ADMIN_USER="$3"
ADMIN_PASSWORD="$4"

SERVER_FQDN="${SERVER_NAME}.database.windows.net"

echo "🚀 Setting up database schema on Azure SQL..."
echo "📡 Server: $SERVER_FQDN"
echo "🗄️ Database: $DATABASE_NAME"
echo ""

# Function to execute SQL script with table existence check
execute_sql_script() {
    local script_file="$1"
    local script_name=$(basename "$script_file")
    
    # Skip if this is a seed file (don't check table existence for seed data)
    if [[ "$script_name" =~ ^Seed.*\.sql$ ]]; then
        echo "📝 Executing $script_name (seed data)..."
        
        if command -v sqlcmd &> /dev/null; then
            sqlcmd -S "$SERVER_FQDN" -d "$DATABASE_NAME" -U "$ADMIN_USER" -P "$ADMIN_PASSWORD" -C -i "$script_file" -b -m 1
            local exit_code=$?
            if [ $exit_code -ne 0 ]; then
                echo "❌ Failed to execute $script_name (exit code: $exit_code)"
                return 1
            else
                echo "✅ $script_name executed successfully"
            fi
        else
            echo "⚠️ sqlcmd not available, skipping database setup"
            return 0
        fi
        return 0
    fi
    
    # For table creation scripts, check if table already exists
    local table_name=""
    if [[ "$script_name" =~ ^([A-Za-z]+)\.sql$ ]]; then
        table_name="${BASH_REMATCH[1]}"
    fi
    
    if [ -n "$table_name" ] && command -v sqlcmd &> /dev/null; then
        echo "🔍 Checking if table $table_name exists..."
        
        # Check if table exists
        local table_exists=$(sqlcmd -S "$SERVER_FQDN" -d "$DATABASE_NAME" -U "$ADMIN_USER" -P "$ADMIN_PASSWORD" -C -Q "IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '$table_name') SELECT 'EXISTS' ELSE SELECT 'NOT_EXISTS'" -h -1 2>/dev/null | tr -d '[:space:]')
        
        if [ "$table_exists" = "EXISTS" ]; then
            echo "✅ Table $table_name already exists, skipping creation"
            return 0
        else
            echo "📝 Creating table $table_name..."
        fi
    else
        echo "📝 Executing $script_name..."
    fi
    
    # Execute the script
    if command -v sqlcmd &> /dev/null; then
        sqlcmd -S "$SERVER_FQDN" -d "$DATABASE_NAME" -U "$ADMIN_USER" -P "$ADMIN_PASSWORD" -C -i "$script_file" -b -m 1
        local exit_code=$?
        if [ $exit_code -ne 0 ]; then
            echo "❌ Failed to execute $script_name (exit code: $exit_code)"
            return 1
        else
            echo "✅ $script_name executed successfully"
        fi
    else
        echo "⚠️ sqlcmd not available, skipping database setup"
        echo "Please install SQL Server command line tools and run scripts manually"
        return 0
    fi
    
    return 0
}

# Execute Authorization module setup FIRST (creates Permissions table)
if [ -f "Authorization/Database/setup-authorization.sql" ]; then
    echo "Setting up Authorization module..."
    execute_sql_script "Authorization/Database/setup-authorization.sql"
    if [ $? -ne 0 ]; then
        exit 1
    fi
else
    echo "Authorization module setup script not found, skipping..."
fi

# Execute core database tables in specific order (dependencies matter)
echo "🏗️ Setting up core database tables..."

# Define the correct order for core table creation
CORE_TABLES=(
    "Database/Company.sql"
    "Database/Users.sql"
    "Database/Role.sql"
    "Database/Customer.sql"
    "Database/Customer_AddUniqueConstraints.sql"
    "Database/Subscription.sql"
    "Database/SubscriptionFeature.sql"
    "Database/Vessels.sql"
    "Database/Sensors.sql"
    "Database/SensorDataPoints.sql"
    "Database/VesselPathPoints.sql"
    "Database/Alert.sql"
    "Database/AlertDelivery.sql"
    "Database/AuditLog.sql"
    "Database/SupportRequests.sql"
    "Database/Feedback.sql"
    "Database/Reports.sql"
    "Database/NotificationPreference.sql"
    "Database/Notification.sql"
)

# Execute core tables first
for table_script in "${CORE_TABLES[@]}"; do
    if [ -f "$table_script" ]; then
        execute_sql_script "$table_script"
        if [ $? -ne 0 ]; then
            echo "❌ Failed to create core table: $(basename "$table_script")"
            exit 1
        fi
    fi
done

# Execute any remaining non-seed SQL files
echo "🔧 Executing remaining database scripts..."
for sql_file in Database/*.sql; do
    filename=$(basename "$sql_file")
    # Skip files we already processed and seed files
    if [ -f "$sql_file" ] && \
       [[ ! " ${CORE_TABLES[@]} " =~ " $sql_file " ]] && \
       [[ ! "$filename" =~ ^Seed.*\.sql$ ]] && \
       [ "$filename" != "EntityPermissions.sql" ] && \
       [ "$filename" != "RolePermission.sql" ]; then
        execute_sql_script "$sql_file"
        if [ $? -ne 0 ]; then
            echo "⚠️ Warning: Failed to execute $filename, continuing..."
        fi
    fi
done

# Execute EntityPermissions.sql (after Authorization module and core tables)
if [ -f "Database/EntityPermissions.sql" ]; then
    echo "🔐 Setting up entity permissions..."
    execute_sql_script "Database/EntityPermissions.sql"
    if [ $? -ne 0 ]; then
        exit 1
    fi
fi

# Execute seed scripts in correct dependency order
echo "🌱 Populating with seed data..."

# 1. First seed users and roles (required by everything else)
if [ -f "Database/SeedUsersAndRoles.sql" ]; then
    execute_sql_script "Database/SeedUsersAndRoles.sql"
    if [ $? -ne 0 ]; then
        exit 1
    fi
fi

# 2. Then seed vessels (required by sensors and path points)
if [ -f "Database/SeedVessels.sql" ]; then
    execute_sql_script "Database/SeedVessels.sql"
    if [ $? -ne 0 ]; then
        exit 1
    fi
fi

# 3. Then seed sensors (depends on vessels)
if [ -f "Database/SeedSensors.sql" ]; then
    execute_sql_script "Database/SeedSensors.sql"
    if [ $? -ne 0 ]; then
        exit 1
    fi
fi

# 4. Then vessel path points (depends on vessels)
if [ -f "Database/SeedVesselPathPoints.sql" ]; then
    execute_sql_script "Database/SeedVesselPathPoints.sql"
    if [ $? -ne 0 ]; then
        echo "⚠️ Warning: SeedVesselPathPoints.sql failed, continuing..."
    fi
fi

# 5. Additional users (depends on roles)
if [ -f "Database/SeedAdditionalUsers.sql" ]; then
    execute_sql_script "Database/SeedAdditionalUsers.sql"
    if [ $? -ne 0 ]; then
        echo "⚠️ Warning: SeedAdditionalUsers.sql failed, continuing..."
    fi
fi

# 6. Finally, role permissions (must be last - depends on roles and permissions)
if [ -f "Database/SeedRolePermissions.sql" ]; then
    execute_sql_script "Database/SeedRolePermissions.sql"
    if [ $? -ne 0 ]; then
        exit 1
    fi
fi

echo "Database setup completed successfully!"
