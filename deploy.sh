#!/bin/bash

# SmartBoat Interactive Azure Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== SmartBoat Azure Deployment Script ===${NC}"

# Check if Azure CLI is installed and user is logged in
if ! command -v az &> /dev/null; then
    echo -e "${RED}Error: Azure CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if logged in to Azure
if ! az account show &> /dev/null; then
    echo -e "${YELLOW}You are not logged in to Azure. Please login first.${NC}"
    az login
fi

# Get current subscription
SUBSCRIPTION=$(az account show --query name -o tsv)
echo -e "${GREEN}Current Azure Subscription: ${SUBSCRIPTION}${NC}"

# 1. Get Azure Container Registry information
echo -e "${YELLOW}=== Container Registry Configuration ===${NC}"
read -p "Enter your Azure Container Registry URL (e.g., myregistry.azurecr.io): " ACR_URL
if [ -z "$ACR_URL" ]; then
    echo -e "${RED}Error: Container Registry URL is required.${NC}"
    exit 1
fi

# Extract registry name from URL
ACR_NAME=$(echo $ACR_URL | cut -d'.' -f1)

# Login to Azure Container Registry
echo -e "${GREEN}Logging into Azure Container Registry...${NC}"
az acr login --name $ACR_NAME

# Define recommended regions for SQL Database
RECOMMENDED_REGIONS=("northeurope" "eastus" "westus2" "eastus2" "centralus" "westeurope")
REGION_NAMES=("North Europe" "East US" "West US 2" "East US 2" "Central US" "West Europe")

# Function to check SQL Database availability in a region
check_sql_availability() {
    local region=$1
    echo -e "${YELLOW}Checking SQL Database availability in ${region}...${NC}"
    
    # Try to create a test server name to check availability
    local test_server="test-availability-$(date +%s)"
    local result=$(az sql server check-name --name $test_server 2>/dev/null)
    
    if [[ $? -eq 0 ]]; then
        return 0  # Available
    else
        return 1  # Not available
    fi
}

# Function to select region with availability check
select_region_with_availability() {
    echo -e "${YELLOW}=== Region Selection ===${NC}"
    echo "Recommended regions for SQL Database deployment:"
    
    for i in "${!RECOMMENDED_REGIONS[@]}"; do
        echo "$((i+1)). ${REGION_NAMES[i]} (${RECOMMENDED_REGIONS[i]})"
    done
    
    while true; do
        echo
        read -p "Select a region (1-${#RECOMMENDED_REGIONS[@]}) or enter custom region name: " REGION_CHOICE
        
        if [[ $REGION_CHOICE =~ ^[0-9]+$ ]] && [[ $REGION_CHOICE -ge 1 ]] && [[ $REGION_CHOICE -le ${#RECOMMENDED_REGIONS[@]} ]]; then
            # User selected a number
            SELECTED_REGION="${RECOMMENDED_REGIONS[$((REGION_CHOICE-1))]}"
            SELECTED_REGION_NAME="${REGION_NAMES[$((REGION_CHOICE-1))]}"
            echo -e "${GREEN}Selected: ${SELECTED_REGION_NAME} (${SELECTED_REGION})${NC}"
            break
        elif [[ $REGION_CHOICE =~ ^[a-zA-Z0-9]+$ ]]; then
            # User entered a custom region name
            SELECTED_REGION="$REGION_CHOICE"
            SELECTED_REGION_NAME="$REGION_CHOICE"
            echo -e "${GREEN}Selected custom region: ${SELECTED_REGION}${NC}"
            break
        else
            echo -e "${RED}Invalid selection. Please enter a number 1-${#RECOMMENDED_REGIONS[@]} or a valid region name.${NC}"
        fi
    done
    
    return 0
}

# 2. Resource Group
RESOURCE_GROUP="smartboat"
echo -e "${GREEN}Using Resource Group: ${RESOURCE_GROUP}${NC}"

# Check if resource group exists, create if not
if ! az group show --name $RESOURCE_GROUP &> /dev/null; then
    echo -e "${YELLOW}Resource Group ${RESOURCE_GROUP} does not exist. Creating...${NC}"
    select_region_with_availability
    REGION="$SELECTED_REGION"
    az group create --name $RESOURCE_GROUP --location $REGION
else
    echo -e "${GREEN}Resource Group ${RESOURCE_GROUP} already exists.${NC}"
    # Get the region of the existing resource group
    REGION=$(az group show --name $RESOURCE_GROUP --query location -o tsv)
    echo -e "${GREEN}Resource Group is in region: ${REGION}${NC}"
fi

# Function to validate Azure resource names
validate_app_service_name() {
    local name=$1
    if [[ ! $name =~ ^[a-zA-Z0-9-]+$ ]]; then
        echo -e "${RED}Error: App Service name can only contain alphanumeric characters and hyphens.${NC}"
        return 1
    fi
    if [[ $name =~ ^- ]] || [[ $name =~ -$ ]]; then
        echo -e "${RED}Error: App Service name cannot start or end with a hyphen.${NC}"
        return 1
    fi
    if [[ ${#name} -gt 63 ]]; then
        echo -e "${RED}Error: App Service name must be less than 64 characters.${NC}"
        return 1
    fi
    return 0
}

validate_sql_server_name() {
    local name=$1
    if [[ ! $name =~ ^[a-zA-Z0-9-]+$ ]]; then
        echo -e "${RED}Error: SQL Server name can only contain alphanumeric characters and hyphens.${NC}"
        return 1
    fi
    if [[ $name =~ ^- ]] || [[ $name =~ -$ ]]; then
        echo -e "${RED}Error: SQL Server name cannot start or end with a hyphen.${NC}"
        return 1
    fi
    if [[ ${#name} -lt 1 ]] || [[ ${#name} -gt 63 ]]; then
        echo -e "${RED}Error: SQL Server name must be between 1 and 63 characters.${NC}"
        return 1
    fi
    return 0
}

# 3. Check for existing App Service
echo -e "${YELLOW}=== App Service Configuration ===${NC}"
read -p "Do you have an existing App Service for SmartBoat? (y/n): " HAS_APP_SERVICE
if [[ $HAS_APP_SERVICE == "y" || $HAS_APP_SERVICE == "Y" ]]; then
    while true; do
        read -p "Enter existing App Service name: " APP_SERVICE_NAME
        if validate_app_service_name "$APP_SERVICE_NAME"; then
            break
        fi
        echo -e "${YELLOW}Please enter a valid App Service name.${NC}"
    done
    
    read -p "Enter existing App Service Plan name: " APP_SERVICE_PLAN_NAME
    
    # Verify resources exist
    if ! az webapp show --name $APP_SERVICE_NAME --resource-group $RESOURCE_GROUP &> /dev/null; then
        echo -e "${RED}Error: App Service ${APP_SERVICE_NAME} not found in resource group ${RESOURCE_GROUP}.${NC}"
        exit 1
    fi
else
    while true; do
        read -p "Enter new App Service name (alphanumeric and hyphens only): " APP_SERVICE_NAME
        if validate_app_service_name "$APP_SERVICE_NAME"; then
            break
        fi
        echo -e "${YELLOW}Please enter a valid App Service name.${NC}"
    done
    
    read -p "Enter new App Service Plan name (or press Enter for default): " APP_SERVICE_PLAN_NAME
    if [ -z "$APP_SERVICE_PLAN_NAME" ]; then
        APP_SERVICE_PLAN_NAME="${APP_SERVICE_NAME}-plan"
    fi
    
    echo -e "${GREEN}Creating App Service Plan and App Service...${NC}"
    
    # Create App Service Plan (Basic B1 - cheapest for containers)
    az appservice plan create \
        --name $APP_SERVICE_PLAN_NAME \
        --resource-group $RESOURCE_GROUP \
        --sku B1 \
        --is-linux
    
    # Create App Service for containers
    az webapp create \
        --name $APP_SERVICE_NAME \
        --resource-group $RESOURCE_GROUP \
        --plan $APP_SERVICE_PLAN_NAME \
        --deployment-container-image-name "nginx:latest"
fi

# 4. Check for existing SQL Database
echo -e "${YELLOW}=== SQL Database Configuration ===${NC}"
read -p "Do you have an existing SQL Database for SmartBoat? (y/n): " HAS_DATABASE
if [[ $HAS_DATABASE == "y" || $HAS_DATABASE == "Y" ]]; then
    while true; do
        read -p "Enter existing SQL Server name: " SQL_SERVER_NAME
        if validate_sql_server_name "$SQL_SERVER_NAME"; then
            break
        fi
        echo -e "${YELLOW}Please enter a valid SQL Server name.${NC}"
    done
    
    read -p "Enter existing SQL Database name: " SQL_DATABASE_NAME
    read -p "Enter SQL admin username: " SQL_ADMIN_USER
    read -s -p "Enter SQL admin password: " SQL_ADMIN_PASSWORD
    echo
    
    # Verify resources exist
    if ! az sql server show --name $SQL_SERVER_NAME --resource-group $RESOURCE_GROUP &> /dev/null; then
        echo -e "${RED}Error: SQL Server ${SQL_SERVER_NAME} not found in resource group ${RESOURCE_GROUP}.${NC}"
        exit 1
    fi
else
    while true; do
        read -p "Enter new SQL Server name (alphanumeric and hyphens only): " SQL_SERVER_NAME
        if validate_sql_server_name "$SQL_SERVER_NAME"; then
            break
        fi
        echo -e "${YELLOW}Please enter a valid SQL Server name.${NC}"
    done
    
    read -p "Enter new SQL Database name (or press Enter for default): " SQL_DATABASE_NAME
    if [ -z "$SQL_DATABASE_NAME" ]; then
        SQL_DATABASE_NAME="SmartBoat"
    fi
    
    read -p "Enter SQL admin username: " SQL_ADMIN_USER
    read -s -p "Enter SQL admin password: " SQL_ADMIN_PASSWORD
    echo
    
    # Select region for SQL Database with availability check
    echo -e "${YELLOW}=== SQL Database Region Selection ===${NC}"
    echo "SQL Database will be created in a region with available capacity."
    echo "Current resource group region: ${REGION}"
    echo
    echo "Options:"
    echo "1. Use same region as resource group (${REGION})"
    echo "2. Select different region for SQL Database"
    echo
    
    while true; do
        read -p "Choose option (1-2): " REGION_OPTION
        if [[ $REGION_OPTION == "1" ]]; then
            SQL_REGION="$REGION"
            echo -e "${GREEN}Using resource group region: ${SQL_REGION}${NC}"
            break
        elif [[ $REGION_OPTION == "2" ]]; then
            select_region_with_availability
            SQL_REGION="$SELECTED_REGION"
            echo -e "${GREEN}Using selected region for SQL Database: ${SQL_REGION}${NC}"
            break
        else
            echo -e "${RED}Invalid option. Please choose 1 or 2.${NC}"
        fi
    done
    
    echo -e "${GREEN}Creating SQL Server and Database in ${SQL_REGION}...${NC}"
    
    # Try to create SQL Server with retry logic
    create_sql_server() {
        local attempt=1
        local max_attempts=3
        
        while [[ $attempt -le $max_attempts ]]; do
            echo -e "${YELLOW}Attempt ${attempt}/${max_attempts}: Creating SQL Server in ${SQL_REGION}...${NC}"
            
            if az sql server create \
                --name $SQL_SERVER_NAME \
                --resource-group $RESOURCE_GROUP \
                --location $SQL_REGION \
                --admin-user $SQL_ADMIN_USER \
                --admin-password $SQL_ADMIN_PASSWORD; then
                echo -e "${GREEN}SQL Server created successfully!${NC}"
                return 0
            else
                echo -e "${RED}Failed to create SQL Server in ${SQL_REGION}${NC}"
                
                if [[ $attempt -lt $max_attempts ]]; then
                    echo -e "${YELLOW}Trying different region...${NC}"
                    # Try next recommended region
                    for region in "${RECOMMENDED_REGIONS[@]}"; do
                        if [[ $region != $SQL_REGION ]]; then
                            SQL_REGION="$region"
                            echo -e "${YELLOW}Switching to region: ${SQL_REGION}${NC}"
                            break
                        fi
                    done
                else
                    echo -e "${RED}Failed to create SQL Server after ${max_attempts} attempts.${NC}"
                    echo -e "${YELLOW}Please try running the script again and select a different region.${NC}"
                    return 1
                fi
            fi
            
            ((attempt++))
        done
    }
    
    # Create SQL Server with retry logic
    if ! create_sql_server; then
        echo -e "${RED}SQL Server creation failed. Exiting...${NC}"
        exit 1
    fi
    
    # Create SQL Database (Basic tier - cheapest)
    echo -e "${GREEN}Creating SQL Database...${NC}"
    az sql db create \
        --name $SQL_DATABASE_NAME \
        --resource-group $RESOURCE_GROUP \
        --server $SQL_SERVER_NAME \
        --edition Basic
    
    # Allow Azure services to access the SQL Server
    echo -e "${GREEN}Configuring SQL Server firewall...${NC}"
    az sql server firewall-rule create \
        --name AllowAzureServices \
        --resource-group $RESOURCE_GROUP \
        --server $SQL_SERVER_NAME \
        --start-ip-address 0.0.0.0 \
        --end-ip-address 0.0.0.0
fi

# 5. Build and push Docker image
echo -e "${YELLOW}=== Building and Pushing Docker Image ===${NC}"
IMAGE_NAME="smartboat-app"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${ACR_URL}/${IMAGE_NAME}:${IMAGE_TAG}"

echo -e "${GREEN}Building Docker image for AMD64 architecture...${NC}"
docker buildx build --platform linux/amd64 -t $FULL_IMAGE_NAME . --push

echo -e "${GREEN}Docker image built and pushed to registry...${NC}"

# 6. Configure App Service with container image
echo -e "${YELLOW}=== Configuring App Service ===${NC}"

# Create connection string
CONNECTION_STRING="Server=${SQL_SERVER_NAME}.database.windows.net;Database=${SQL_DATABASE_NAME};User ID=${SQL_ADMIN_USER};Password=${SQL_ADMIN_PASSWORD};Encrypt=True;Connection Timeout=30;"

# Generate JWT secret key
JWT_SECRET_KEY=$(openssl rand -base64 32)

# Generate security salt
SECURITY_SALT=$(openssl rand -base64 16)

# Configure container image (using updated command)
az webapp config set \
    --name $APP_SERVICE_NAME \
    --resource-group $RESOURCE_GROUP \
    --linux-fx-version "DOCKER|${FULL_IMAGE_NAME}"

# Set application settings
az webapp config appsettings set \
    --name $APP_SERVICE_NAME \
    --resource-group $RESOURCE_GROUP \
    --settings \
    "ConnectionStrings__DefaultConnection=$CONNECTION_STRING" \
    "ConnectionString=$CONNECTION_STRING" \
    "JwtSecretKey=$JWT_SECRET_KEY" \
    "SecuritySalt=$SECURITY_SALT" \
    "AppServiceUrl=https://${APP_SERVICE_NAME}.azurewebsites.net" \
    "ASPNETCORE_ENVIRONMENT=Production" \
    "WEBSITES_PORT=80" \
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE=false"

# Enable container logging
echo -e "${GREEN}Enabling container logging...${NC}"
az webapp log config \
    --name $APP_SERVICE_NAME \
    --resource-group $RESOURCE_GROUP \
    --docker-container-logging filesystem

# Configure health check
echo -e "${GREEN}Configuring health check...${NC}"
az webapp config appsettings set \
    --name $APP_SERVICE_NAME \
    --resource-group $RESOURCE_GROUP \
    --settings \
    "WEBSITE_HEALTHCHECK_MAXPINGFAILURES=10"

# 7. Setup Database Schema
echo -e "${YELLOW}=== Setting up Database Schema ===${NC}"
echo -e "${GREEN}Running database setup scripts...${NC}"

# Install sqlcmd if not available (try to install automatically)
if ! command -v sqlcmd &> /dev/null; then
    echo -e "${YELLOW}Installing SQL Server command line tools...${NC}"
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        curl -sSL https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -
        curl -sSL https://packages.microsoft.com/config/ubuntu/20.04/prod.list | sudo tee /etc/apt/sources.list.d/msprod.list
        sudo apt-get update
        sudo ACCEPT_EULA=Y apt-get install -y mssql-tools unixodbc-dev
        export PATH="$PATH:/opt/mssql-tools/bin"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        if command -v brew &> /dev/null; then
            echo -e "${YELLOW}Installing sqlcmd via Homebrew...${NC}"
            brew tap microsoft/mssql-release https://github.com/Microsoft/homebrew-mssql-release
            brew update
            HOMEBREW_NO_ENV_FILTERING=1 ACCEPT_EULA=Y brew install msodbcsql17 mssql-tools
        else
            echo -e "${YELLOW}Homebrew not found. Skipping automatic sqlcmd installation.${NC}"
            echo -e "${YELLOW}Please install Homebrew first: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"${NC}"
            echo -e "${YELLOW}Then run: brew tap microsoft/mssql-release && ACCEPT_EULA=Y brew install mssql-tools${NC}"
        fi
    fi
fi

# Change to the API directory where the database scripts are located
cd SmartBoat.API

# Check if database setup scripts exist
if [ -d "Database" ]; then
    echo -e "${GREEN}Found database scripts, setting up schema...${NC}"
    
    # Create database setup script for Azure SQL
    cat > ../setup-azure-database.sh << 'EOF'
#!/bin/bash
# Azure SQL Database Setup Script

# Parameters passed from deployment script
SERVER_NAME="$1"
DATABASE_NAME="$2"
ADMIN_USER="$3"
ADMIN_PASSWORD="$4"

SERVER_FQDN="${SERVER_NAME}.database.windows.net"

echo "🚀 Setting up database schema on Azure SQL..."
echo "📡 Server: $SERVER_FQDN"
echo "🗄️ Database: $DATABASE_NAME"
echo ""

# Function to execute SQL script with table existence check
execute_sql_script() {
    local script_file="$1"
    local script_name=$(basename "$script_file")
    
    # Skip if this is a seed file (don't check table existence for seed data)
    if [[ "$script_name" =~ ^Seed.*\.sql$ ]]; then
        echo "📝 Executing $script_name (seed data)..."
        
        if command -v sqlcmd &> /dev/null; then
            sqlcmd -S "$SERVER_FQDN" -d "$DATABASE_NAME" -U "$ADMIN_USER" -P "$ADMIN_PASSWORD" -C -i "$script_file" -b -m 1
            local exit_code=$?
            if [ $exit_code -ne 0 ]; then
                echo "❌ Failed to execute $script_name (exit code: $exit_code)"
                return 1
            else
                echo "✅ $script_name executed successfully"
            fi
        else
            echo "⚠️ sqlcmd not available, skipping database setup"
            return 0
        fi
        return 0
    fi
    
    # For table creation scripts, check if table already exists
    local table_name=""
    if [[ "$script_name" =~ ^([A-Za-z]+)\.sql$ ]]; then
        table_name="${BASH_REMATCH[1]}"
    fi
    
    if [ -n "$table_name" ] && command -v sqlcmd &> /dev/null; then
        echo "🔍 Checking if table $table_name exists..."
        
        # Check if table exists
        local table_exists=$(sqlcmd -S "$SERVER_FQDN" -d "$DATABASE_NAME" -U "$ADMIN_USER" -P "$ADMIN_PASSWORD" -C -Q "IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '$table_name') SELECT 'EXISTS' ELSE SELECT 'NOT_EXISTS'" -h -1 2>/dev/null | tr -d '[:space:]')
        
        if [ "$table_exists" = "EXISTS" ]; then
            echo "✅ Table $table_name already exists, skipping creation"
            return 0
        else
            echo "📝 Creating table $table_name..."
        fi
    else
        echo "📝 Executing $script_name..."
    fi
    
    # Execute the script
    if command -v sqlcmd &> /dev/null; then
        sqlcmd -S "$SERVER_FQDN" -d "$DATABASE_NAME" -U "$ADMIN_USER" -P "$ADMIN_PASSWORD" -C -i "$script_file" -b -m 1
        local exit_code=$?
        if [ $exit_code -ne 0 ]; then
            echo "❌ Failed to execute $script_name (exit code: $exit_code)"
            return 1
        else
            echo "✅ $script_name executed successfully"
        fi
    else
        echo "⚠️ sqlcmd not available, skipping database setup"
        echo "Please install SQL Server command line tools and run scripts manually"
        return 0
    fi
    
    return 0
}

# Execute Authorization module setup FIRST (creates Permissions table)
if [ -f "Authorization/Database/setup-authorization.sql" ]; then
    echo "Setting up Authorization module..."
    execute_sql_script "Authorization/Database/setup-authorization.sql"
    if [ $? -ne 0 ]; then
        exit 1
    fi
else
    echo "Authorization module setup script not found, skipping..."
fi

# Execute core database tables in specific order (dependencies matter)
echo "🏗️ Setting up core database tables..."

# Define the correct order for core table creation
CORE_TABLES=(
    "Database/Company.sql"
    "Database/Users.sql"
    "Database/Role.sql"
    "Database/Customer.sql"
    "Database/Customer_AddUniqueConstraints.sql"
    "Database/Subscription.sql"
    "Database/SubscriptionFeature.sql"
    "Database/Vessels.sql"
    "Database/Sensors.sql"
    "Database/SensorDataPoints.sql"
    "Database/VesselPathPoints.sql"
    "Database/Alert.sql"
    "Database/AlertDelivery.sql"
    "Database/AuditLog.sql"
    "Database/SupportRequests.sql"
    "Database/Feedback.sql"
    "Database/Reports.sql"
    "Database/NotificationPreference.sql"
    "Database/Notification.sql"
)

# Execute core tables first
for table_script in "${CORE_TABLES[@]}"; do
    if [ -f "$table_script" ]; then
        execute_sql_script "$table_script"
        if [ $? -ne 0 ]; then
            echo "❌ Failed to create core table: $(basename "$table_script")"
            exit 1
        fi
    fi
done

# Execute any remaining non-seed SQL files
echo "🔧 Executing remaining database scripts..."
for sql_file in Database/*.sql; do
    filename=$(basename "$sql_file")
    # Skip files we already processed and seed files
    if [ -f "$sql_file" ] && \
       [[ ! " ${CORE_TABLES[@]} " =~ " $sql_file " ]] && \
       [[ ! "$filename" =~ ^Seed.*\.sql$ ]] && \
       [ "$filename" != "EntityPermissions.sql" ] && \
       [ "$filename" != "RolePermission.sql" ]; then
        execute_sql_script "$sql_file"
        if [ $? -ne 0 ]; then
            echo "⚠️ Warning: Failed to execute $filename, continuing..."
        fi
    fi
done

# Execute EntityPermissions.sql (after Authorization module and core tables)
if [ -f "Database/EntityPermissions.sql" ]; then
    echo "🔐 Setting up entity permissions..."
    execute_sql_script "Database/EntityPermissions.sql"
    if [ $? -ne 0 ]; then
        exit 1
    fi
fi

# Execute seed scripts in correct dependency order
echo "🌱 Populating with seed data..."

# 1. First seed users and roles (required by everything else)
if [ -f "Database/SeedUsersAndRoles.sql" ]; then
    execute_sql_script "Database/SeedUsersAndRoles.sql"
    if [ $? -ne 0 ]; then
        exit 1
    fi
fi

# 2. Then seed vessels (required by sensors and path points)
if [ -f "Database/SeedVessels.sql" ]; then
    execute_sql_script "Database/SeedVessels.sql"
    if [ $? -ne 0 ]; then
        exit 1
    fi
fi

# 3. Then seed sensors (depends on vessels)
if [ -f "Database/SeedSensors.sql" ]; then
    execute_sql_script "Database/SeedSensors.sql"
    if [ $? -ne 0 ]; then
        exit 1
    fi
fi

# 4. Then vessel path points (depends on vessels)
if [ -f "Database/SeedVesselPathPoints.sql" ]; then
    execute_sql_script "Database/SeedVesselPathPoints.sql"
    if [ $? -ne 0 ]; then
        echo "⚠️ Warning: SeedVesselPathPoints.sql failed, continuing..."
    fi
fi

# 5. Additional users (depends on roles)
if [ -f "Database/SeedAdditionalUsers.sql" ]; then
    execute_sql_script "Database/SeedAdditionalUsers.sql"
    if [ $? -ne 0 ]; then
        echo "⚠️ Warning: SeedAdditionalUsers.sql failed, continuing..."
    fi
fi

# 6. Finally, role permissions (must be last - depends on roles and permissions)
if [ -f "Database/SeedRolePermissions.sql" ]; then
    execute_sql_script "Database/SeedRolePermissions.sql"
    if [ $? -ne 0 ]; then
        exit 1
    fi
fi

echo "Database setup completed successfully!"
EOF
    
    chmod +x ../setup-azure-database.sh
    
    # Check if sqlcmd is now available before running database setup
    if command -v sqlcmd &> /dev/null; then
        echo -e "${GREEN}sqlcmd found, proceeding with database setup...${NC}"
        # Run the database setup
        ../setup-azure-database.sh "$SQL_SERVER_NAME" "$SQL_DATABASE_NAME" "$SQL_ADMIN_USER" "$SQL_ADMIN_PASSWORD"
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Database schema setup completed successfully!${NC}"
        else
            echo -e "${YELLOW}⚠️ Database setup encountered issues.${NC}"
            echo -e "${YELLOW}You may need to run the scripts manually.${NC}"
            echo -e "${YELLOW}Use: ./setup-database-manually.sh${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ sqlcmd not available after installation attempt.${NC}"
        echo -e "${YELLOW}Please install SQL Server command line tools manually:${NC}"
        echo -e "${YELLOW}  macOS: brew tap microsoft/mssql-release && ACCEPT_EULA=Y brew install mssql-tools${NC}"
        echo -e "${YELLOW}  Linux: Install mssql-tools package${NC}"
        echo -e "${YELLOW}Then run: ./setup-database-manually.sh${NC}"
    fi
else
    echo -e "${YELLOW}No database scripts found, skipping database setup${NC}"
fi

# Return to original directory
cd ..

# 8. Restart App Service
echo -e "${GREEN}Restarting App Service...${NC}"
az webapp restart --name $APP_SERVICE_NAME --resource-group $RESOURCE_GROUP

# 8. Get App Service URL
APP_URL=$(az webapp show --name $APP_SERVICE_NAME --resource-group $RESOURCE_GROUP --query defaultHostName -o tsv)

echo -e "${GREEN}=== Deployment Complete ===${NC}"
echo -e "${GREEN}App Service URL: https://${APP_URL}${NC}"
echo -e "${GREEN}Resource Group: ${RESOURCE_GROUP}${NC}"
echo -e "${GREEN}App Service: ${APP_SERVICE_NAME}${NC}"
echo -e "${GREEN}SQL Server: ${SQL_SERVER_NAME}${NC}"
echo -e "${GREEN}SQL Database: ${SQL_DATABASE_NAME}${NC}"

echo -e "${YELLOW}Note: It may take a few minutes for the application to start up completely.${NC}"
echo -e "${YELLOW}You can monitor the deployment in the Azure Portal or using 'az webapp log tail'.${NC}"