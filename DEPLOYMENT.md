# SmartBoat Azure Deployment Guide

This guide provides step-by-step instructions for deploying the SmartBoat application to Azure using a single container approach.

## Overview

The deployment creates a single Docker container that includes:
- **React Frontend** - Built and served by nginx
- **.NET Core API** - Running on port 5000
- **Nginx** - Serves React app and proxies API calls
- **Azure SQL Database** - External managed database

## Prerequisites

Before deploying, ensure you have:

1. **Azure CLI** installed and configured
2. **Docker** installed and running
3. **Azure Container Registry** (ACR) created
4. **Azure subscription** with appropriate permissions

### Install Azure CLI

```bash
# macOS
brew install azure-cli

# Windows
winget install Microsoft.AzureCLI

# Linux
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
```

### Login to Azure

```bash
az login
az account set --subscription "your-subscription-id"
```

## Quick Deployment

The easiest way to deploy is using the interactive script:

```bash
./deploy.sh
```

The script will prompt you for:
- Azure Container Registry URL
- Whether you have existing App Service and SQL Database
- Resource names and credentials
- Azure region (if creating new resources)

## Manual Deployment Steps

If you prefer to deploy manually, follow these steps:

### 1. Build and Push Docker Image

```bash
# Login to your Azure Container Registry
az acr login --name your-registry-name

# Build the Docker image
docker build -t your-registry.azurecr.io/smartboat-app:latest .

# Push to registry
docker push your-registry.azurecr.io/smartboat-app:latest
```

### 2. Create Azure Resources

#### Option A: Using Bicep Templates

```bash
# Create resource group
az group create --name smartboat --location eastus

# Deploy infrastructure
az deployment group create \
  --resource-group smartboat \
  --template-file infrastructure/main.bicep \
  --parameters sqlAdminUsername=sqladmin \
  --parameters sqlAdminPassword=YourStrongPassword123! \
  --parameters dockerImage=your-registry.azurecr.io/smartboat-app:latest
```

#### Option B: Using Azure CLI Commands

```bash
# Create resource group
az group create --name smartboat --location eastus

# Create App Service Plan
az appservice plan create \
  --name smartboat-plan \
  --resource-group smartboat \
  --sku B1 \
  --is-linux

# Create App Service
az webapp create \
  --name smartboat-app \
  --resource-group smartboat \
  --plan smartboat-plan \
  --deployment-container-image-name your-registry.azurecr.io/smartboat-app:latest

# Create SQL Server
az sql server create \
  --name smartboat-sql-server \
  --resource-group smartboat \
  --admin-user sqladmin \
  --admin-password YourStrongPassword123!

# Create SQL Database
az sql db create \
  --name SmartBoat \
  --resource-group smartboat \
  --server smartboat-sql-server \
  --edition Basic

# Allow Azure services to access SQL Server
az sql server firewall-rule create \
  --name AllowAzureServices \
  --resource-group smartboat \
  --server smartboat-sql-server \
  --start-ip-address 0.0.0.0 \
  --end-ip-address 0.0.0.0
```

### 3. Configure App Service

```bash
# Set connection string
az webapp config appsettings set \
  --name smartboat-app \
  --resource-group smartboat \
  --settings "ConnectionStrings__DefaultConnection=Server=smartboat-sql-server.database.windows.net;Database=SmartBoat;User ID=sqladmin;Password=YourStrongPassword123!;Encrypt=True;Connection Timeout=30;"

# Set environment variables
az webapp config appsettings set \
  --name smartboat-app \
  --resource-group smartboat \
  --settings \
  "ASPNETCORE_ENVIRONMENT=Production" \
  "WEBSITES_PORT=80"

# Configure container image
az webapp config container set \
  --name smartboat-app \
  --resource-group smartboat \
  --docker-custom-image-name your-registry.azurecr.io/smartboat-app:latest \
  --docker-registry-server-url https://your-registry.azurecr.io
```

### 4. Set Up Database

Run the database setup script to create tables and seed data:

```bash
# Navigate to the database directory
cd SmartBoat.API/Database

# Run setup script (requires sqlcmd)
./setup-database.sh
```

Or manually execute SQL scripts against your Azure SQL Database.

### 5. Restart App Service

```bash
az webapp restart --name smartboat-app --resource-group smartboat
```

## Configuration Files

### Production Configuration

The following files are configured for production:

- **`appsettings.Production.json`** - .NET API production settings
- **`.env.production`** - React frontend environment variables
- **`nginx.conf`** - Nginx configuration for serving React + API proxy
- **`startup.sh`** - Container startup script

### Environment Variables

Key environment variables set in App Service:

- `ASPNETCORE_ENVIRONMENT=Production`
- `WEBSITES_PORT=80`
- `ConnectionStrings__DefaultConnection=<SQL connection string>`

## Monitoring and Troubleshooting

### View Application Logs

```bash
# Stream logs
az webapp log tail --name smartboat-app --resource-group smartboat

# Download logs
az webapp log download --name smartboat-app --resource-group smartboat
```

### Health Check

The nginx configuration includes a health check endpoint:

```bash
curl https://your-app.azurewebsites.net/health
```

### Common Issues

1. **Container won't start**
   - Check that Docker image builds successfully locally
   - Verify container registry credentials
   - Review application logs for startup errors

2. **Database connection issues**
   - Verify SQL Server firewall rules
   - Check connection string format
   - Ensure database exists and is accessible

3. **React app not loading**
   - Verify nginx configuration
   - Check that React build completed successfully
   - Ensure static files are in the correct location

4. **Region Availability Issues**
   - **Problem**: "Location 'X' is not accepting creation of new Windows Azure SQL Database servers"
   - **Solution**: The deployment script now includes automatic region selection and fallback
   - **Manual Fix**: Run the script again and select a different region from the recommended list
   - **Recommended Regions**: North Europe, East US, West US 2, East US 2, Central US
   - **Note**: SQL Database can be in a different region than your App Service

### Region Selection Guide

The deployment script provides enhanced region selection to handle capacity issues:

1. **Initial Deployment**
   - Script shows recommended regions with good availability
   - Select from numbered list or enter custom region
   - Automatic fallback if selected region is unavailable

2. **SQL Database Region Selection**
   - Option to use same region as resource group
   - Option to select different region for SQL Database
   - Automatic retry with different regions if creation fails

3. **Multi-Region Deployment**
   - App Service and SQL Database can be in different regions
   - No performance impact for typical web applications
   - Connection string automatically handles region differences

### Recommended Regions by Service

**SQL Database** (in order of recommendation):
1. North Europe (`northeurope`)
2. East US (`eastus`)  
3. West US 2 (`westus2`)
4. East US 2 (`eastus2`)
5. Central US (`centralus`)

**App Service** (generally available in all regions):
- Use same region as your users for best performance
- West Europe, North Europe for European users
- East US, West US 2 for US users

## Security Considerations

- **HTTPS Only**: App Service enforces HTTPS
- **SQL Server**: Configured with Azure AD authentication
- **Firewall**: SQL Server allows only Azure services
- **Secrets**: Use Azure Key Vault for production secrets

## Cost Optimization

Current configuration uses the cheapest Azure tiers:
- **App Service Basic B1**: ~$13/month
- **SQL Database Basic**: ~$5/month
- **Total**: ~$18/month

To reduce costs further:
- Use Azure Container Instances for lower traffic
- Consider serverless SQL for intermittent usage
- Implement auto-scaling to scale down during low usage

## Updating the Application

To update the deployed application:

1. Build and push new Docker image:
   ```bash
   docker build -t your-registry.azurecr.io/smartboat-app:latest .
   docker push your-registry.azurecr.io/smartboat-app:latest
   ```

2. Restart App Service:
   ```bash
   az webapp restart --name smartboat-app --resource-group smartboat
   ```

App Service will automatically pull and deploy the latest image.

## Scaling and Performance

### Horizontal Scaling

```bash
# Scale out to multiple instances
az appservice plan update \
  --name smartboat-plan \
  --resource-group smartboat \
  --number-of-workers 2
```

### Vertical Scaling

```bash
# Upgrade to higher tier
az appservice plan update \
  --name smartboat-plan \
  --resource-group smartboat \
  --sku S1
```

## Backup and Disaster Recovery

### Database Backup

Azure SQL Database provides automatic backups. Configure long-term retention:

```bash
az sql db ltr-policy set \
  --resource-group smartboat \
  --server smartboat-sql-server \
  --database SmartBoat \
  --weekly-retention P12W \
  --monthly-retention P12M
```

### App Service Backup

```bash
# Create backup
az webapp config backup create \
  --resource-group smartboat \
  --webapp-name smartboat-app \
  --backup-name backup-$(date +%Y%m%d) \
  --container-url "<storage-account-url>"
```

## Support and Resources

- **Azure Documentation**: https://docs.microsoft.com/azure
- **App Service**: https://docs.microsoft.com/azure/app-service
- **SQL Database**: https://docs.microsoft.com/azure/sql-database
- **Container Registry**: https://docs.microsoft.com/azure/container-registry