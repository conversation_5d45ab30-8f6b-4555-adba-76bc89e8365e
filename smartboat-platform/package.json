{"name": "smartboat-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview --port 5173 --host", "serve": "vite preview --port 5173 --host"}, "dependencies": {"@tailwindcss/vite": "^4.1.5", "@tanstack/react-query": "^5.76.0", "@tanstack/react-query-devtools": "^5.76.0", "@types/leaflet": "^1.9.20", "@types/react-datepicker": "^6.2.0", "i18next": "^25.1.2", "i18next-browser-languagedetector": "^8.1.0", "leaflet": "^1.9.4", "react": "^19.1.0", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.1", "react-router-dom": "^7.6.0", "recharts": "^2.10.3", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.5", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "vite": "^6.3.5"}}