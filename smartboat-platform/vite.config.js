import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import fs from 'fs'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
    // Custom plugin to handle SPA routing
    {
      name: 'spa-fallback',
      configureServer(server) {
        // Return middleware to handle SPA routing
        return () => {
          server.middlewares.use((req, res, next) => {
            // For Vite's internal endpoints and static assets
            if (req.url.includes('.') || req.url.startsWith('/@') || req.url.startsWith('/__')) {
              return next()
            }
            
            // For API endpoints
            if (req.url.startsWith('/api')) {
              return next()
            }
            
            // For all other routes, serve index.html
            const indexPath = path.resolve('index.html')
            if (fs.existsSync(indexPath)) {
              res.writeHead(200, { 'Content-Type': 'text/html' })
              res.end(fs.readFileSync(indexPath))
              return
            }
            
            next()
          })
        }
      }
    }
  ],
  base: '/',
  server: {
    open: true,
    port: 5173,
    // This is the most important setting for SPA routing
    historyApiFallback: {
      disableDotRule: true,
      index: '/index.html'
    }
  },
  preview: {
    // Also apply history fallback to preview server
    historyApiFallback: true
  },
  build: {
    // Single page app
    outDir: 'dist',
    emptyOutDir: true,
  },
  resolve: {
    // Enable TypeScript file extensions
    extensions: ['.js', '.jsx', '.ts', '.tsx']
  }
})