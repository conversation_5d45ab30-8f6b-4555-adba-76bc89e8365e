import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import './sidebar.css';
import { useAuth } from '../../context/AuthContext';
import smartboatLogo from '../../assets/smartboat-logo.png';
import smartboatLogoSmall from '../../assets/smartboat-logo-small.png';

interface SidebarProps {
  activePage: string;
  setActivePage: (page: string) => void;
  onExpandChange?: (expanded: boolean) => void;
  onLogout?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activePage, setActivePage, onExpandChange, onLogout }) => {
  const [expanded, setExpanded] = useState(true);
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Toggle sidebar expansion
  const toggleSidebar = () => {
    const newExpandedState = !expanded;
    setExpanded(newExpandedState);
    if (onExpandChange) {
      onExpandChange(newExpandedState);
    }
  };

  // All navigation items
  const allNavItems = [
    {
      id: 'dashboard',
      label: t('common.dashboard'),
      role: ['Administrator', 'Customer', 'Manager', 'Technician', 'Viewer'], // All roles can see this
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
        </svg>
      )
    },
    {
      id: 'customers',
      label: t('common.customers'),
      role: ['Administrator', 'Manager'], // Only admin and managers can see customers
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
        </svg>
      )
    },
    {
      id: 'companies',
      label: t('common.companies'),
      role: ['Administrator', 'Manager', 'Customer'], // Added customer to roles that can see companies
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: 'vessels',
      label: t('common.vessels'),
      role: ['Administrator', 'Customer', 'Manager', 'Technician'], // Most roles can see vessels
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M13 7H7v6h6V7z" />
          <path fillRule="evenodd" d="M7 2a1 1 0 012 0v1h2V2a1 1 0 112 0v1h2a2 2 0 012 2v2h1a1 1 0 110 2h-1v2h1a1 1 0 110 2h-1v2a2 2 0 01-2 2h-2v1a1 1 0 11-2 0v-1H9v1a1 1 0 11-2 0v-1H5a2 2 0 01-2-2v-2H2a1 1 0 110-2h1V9H2a1 1 0 010-2h1V5a2 2 0 012-2h2V2zM5 5h10v10H5V5z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: 'sensors',
      label: t('common.sensors'),
      role: ['Administrator', 'Customer', 'Manager', 'Technician'], // Most roles can see sensors
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: 'subscriptions',
      label: t('common.subscriptions'),
      role: ['Administrator', 'Customer', 'Manager'], // Admin, Customer, and Manager can see subscriptions
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: 'profile',
      label: t('common.profile'),
      role: ['Administrator', 'Customer', 'Manager', 'Technician', 'Viewer'], // All roles can see profile
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
        </svg>
      )
    }
  ];

  // Filter navigation items based on user role
  const navItems = allNavItems.filter(item => {
    // If no current user or role not specified, show all items for development
    if (!currentUser || !currentUser.role) return true;
    // Show only items allowed for the current user's role
    return item.role.includes(currentUser.role);
  });

  // We no longer need a separate navigation handler since we're 
  // using React Router's Link components with onClick handlers

  return (
    <aside
      className={`${expanded ? 'w-64' : 'w-20'} h-screen bg-white dark:bg-gray-900 text-gray-800 dark:text-white transition-all duration-300 ease-in-out fixed left-0 top-0 z-50 sidebar-scroll overflow-x-hidden`}
    >
      {/* Logo and Toggle Button */}
      <div className={`flex items-center justify-between ${expanded ? 'p-4' : 'py-4 px-3'} border-b border-gray-200 dark:border-gray-800 ${!expanded ? 'h-[72px]' : ''}`}>
        <div
          className="flex items-center cursor-pointer"
          onClick={() => {
            // Set explicit dashboard navigation flag
            sessionStorage.setItem('dashboard_explicit', 'true');
            // Save dashboard path in localStorage to avoid redirection loops
            localStorage.setItem('smartboat_last_path', '/dashboard');

            setActivePage('dashboard');
            navigate('/dashboard');
          }}
        >
          <div className={`bg-white dark:bg-gray-200 rounded-full p-1 flex justify-center items-center ${!expanded ? 'h-10 w-10' : ''}`}>
            <img
              src={expanded ? smartboatLogo : smartboatLogoSmall}
              alt="SmartBoat Logo"
              className={`${expanded ? 'h-8 w-auto' : 'h-8 w-8'}`}
            />
          </div>
          {expanded && <span className="ml-2 font-bold text-xl">SmartBoat</span>}
        </div>
        <button
          onClick={toggleSidebar}
          className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none"
        >
          {expanded ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          )}
        </button>
      </div>

      {/* Navigation Menu */}
      <div className="py-4 overflow-y-auto overflow-x-hidden">
        <nav>
          <ul className="space-y-2 px-2">
            {navItems.map((item) => (
              <li key={item.id} className={`sidebar-menu-item ${activePage === item.id ? 'active' : ''}`}>
                <Link
                  to={`/${item.id}`}
                  className={`flex items-center ${expanded ? 'justify-start px-4' : 'justify-center'} py-3 w-full rounded-lg transition-colors duration-200 ${
                    activePage === item.id
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
                  }`}
                  onClick={(e) => {
                    // Only perform app state updates, let the router handle navigation
                    setActivePage(item.id);
                    
                    // Special handling for dashboard navigation
                    if (item.id === 'dashboard') {
                      // Set explicit dashboard navigation flag
                      sessionStorage.setItem('dashboard_explicit', 'true');
                      // Save dashboard path in localStorage to avoid redirection loops
                      localStorage.setItem('smartboat_last_path', '/dashboard');
                    }
                  }}
                >
                  <div className={`flex-shrink-0 ${expanded ? '' : 'p-1 sidebar-tooltip'}`}>
                    {item.icon}
                    {!expanded && <span className="tooltip-text">{item.label}</span>}
                  </div>
                  {expanded && <span className="ml-3 truncate">{item.label}</span>}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </div>

      {/* User Profile Section */}
      <div className="absolute bottom-0 left-0 right-0 border-t border-gray-200 dark:border-gray-800 overflow-hidden">
        {/* Logout button */}
        <div className="px-4 py-2">
          <button
            className={`flex items-center ${expanded ? 'justify-start w-full' : 'justify-center'} py-2 px-3 rounded-lg hover:bg-red-100 dark:hover:bg-red-700 text-red-600 dark:text-gray-300 hover:text-red-700 dark:hover:text-white transition-colors duration-200`}
            onClick={onLogout}
          >
            <div className={`flex-shrink-0 ${expanded ? '' : 'p-1 sidebar-tooltip'}`}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-5-5H3zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
              </svg>
              {!expanded && <span className="tooltip-text">{t('common.logout')}</span>}
            </div>
            {expanded && <span className="ml-3 truncate">{t('common.logout')}</span>}
          </button>
        </div>

        {/* User profile */}
        <div className="p-4">
          <Link
            to="/profile/security"
            className={`flex ${expanded ? 'items-center' : 'flex-col items-center'} cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 p-2 rounded-lg user-profile`}
            onClick={() => {
              // Just use standard routing, don't manipulate internal app state
              // This ensures URL is properly updated and browser history works
              setActivePage('profile');
              // Let the router handle the actual navigation
            }}
          >
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center">
                {currentUser?.name ? (
                  <span className="text-white font-semibold">
                    {currentUser.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </span>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
            </div>
            {expanded && (
              <div className="ml-3 overflow-hidden">
                <p className="text-sm font-medium text-gray-800 dark:text-white truncate">{currentUser?.name || 'User'}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{currentUser?.email || '<EMAIL>'}</p>
                <p className="text-xs text-blue-600 dark:text-blue-400 truncate">{currentUser?.role || 'Role'}</p>
              </div>
            )}
          </Link>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;