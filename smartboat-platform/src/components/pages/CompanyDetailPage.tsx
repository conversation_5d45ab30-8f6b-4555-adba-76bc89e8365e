import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { StatusBadge, LoadingOverlay, ErrorNotification } from '../common';
import { Company } from '../../types';
import { useAuth } from '../../context/AuthContext';
import EditCompanyForm from '../features/companies/EditCompanyForm';
import { useCompany, useCompanyVessels, useDeleteCompany } from '../../hooks/queries/useCompanyQueries';
import { useNotifications } from '../../hooks/queries/useNotificationQueries';

const CompanyDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useAuth();
  const { t } = useTranslation();
  
  // Keep companyId as string for UUID support
  const companyId = id;
  
  const [activeTab, setActiveTab] = useState('overview');
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(30); // Width in percentage
  const [isDragging, setIsDragging] = useState(false); // Add state for dragging
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const deleteCompany = useDeleteCompany();

  const isAdmin = currentUser?.role === 'Administrator' || currentUser?.role === 'Manager';

  // Fetch company data using React Query
  const {
    data: company,
    isLoading: companyLoading,
    isError: companyError,
    error,
    refetch
  } = useCompany(companyId, {
    useMockFallback: true,
    staleTime: 0,
    cacheTime: 0,
    retry: false,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false
  });

  // Fetch company vessels using React Query
  const {
    data: companyVessels = [],
    isLoading: vesselsLoading,
    isError: vesselsError
  } = useCompanyVessels(companyId, {
    useMockFallback: true,
    staleTime: 0,
    cacheTime: 0,
    retry: false,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false
  });

  // Fetch recent notifications for the current user
  const {
    data: companyNotifications = [],
    isLoading: notificationsLoading
  } = useNotifications({
    pageLimit: 5, // Show only recent 5 notifications
    useMockFallback: true
  });

  // Helper function to get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'error':
        return (
          <div className="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center text-red-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'warning':
        return (
          <div className="h-8 w-8 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'success':
        return (
          <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center text-green-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clipRule="evenodd" />
            </svg>
          </div>
        );
    }
  };

  // Helper function to format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return t('notifications.justNow');
    if (diffMins < 60) return t('notifications.minutesAgo', { count: diffMins });
    if (diffHours < 24) return t('notifications.hoursAgo', { count: diffHours });
    if (diffDays < 7) return t('notifications.daysAgo', { count: diffDays });
    
    return date.toLocaleDateString();
  };
  
  // Check if we should open edit modal based on location state
  useEffect(() => {
    if (location.state && location.state.editMode) {
      setIsEditModalOpen(true);
    }
  }, [location.state]);
  
  // Combined loading state - wait for ALL calls to complete
  const isLoading = companyLoading || vesselsLoading;

  // Handle company update
  const handleSaveCompany = (updatedCompany: Company) => {
    setIsEditModalOpen(false);
    // Refetch company data to get the latest from the API
    refetch();
    console.log('Company updated:', updatedCompany);
  };

  // Handle company deletion
  const handleDeleteCompany = async () => {
    try {
      await deleteCompany.mutateAsync(companyId);
      setShowDeleteConfirm(false);
      navigate('/companies');
    } catch (error) {
      console.error('Failed to delete company:', error);
      alert(t('companies.deleteFailed'));
    }
  };

  // Handle vessel selection
  const handleSelectVessel = (vesselId: string) => {
    navigate(`/vessels/${vesselId}`);
  };

  // Improved resize handler with isDragging state
  function startResize(e: React.MouseEvent) {
    e.preventDefault();
    setIsDragging(true);
    
    // Define handlers inline to avoid hook issues
    const onMouseMove = (moveEvent: MouseEvent) => {
      // Calculate new width based on mouse position
      const windowWidth = window.innerWidth;
      const mouseX = moveEvent.clientX;
      
      // Convert to percentage (100 - x because we're dragging from right side)
      const widthPercent = 100 - (mouseX / windowWidth * 100);
      
      // Limit to reasonable values (between 20% and 80%)
      const newWidth = Math.max(20, Math.min(80, widthPercent));
      setSidebarWidth(newWidth);
    };
    
    const onMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };
    
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto relative">
        <LoadingOverlay 
          message="Loading company data..."
          subMessage="Fetching company details and vessels..."
        />
      </div>
    );
  }

  // Error state
  if (companyError) {
    return (
      <div className="container mx-auto">
        <ErrorNotification
          message={t('common.errorFetchingData')}
          error={error}
          onRetry={refetch}
        />
      </div>
    );
  }

  // Company not found
  if (!company) {
    return (
      <div className="bg-white rounded-lg shadow p-8 text-center">
        <h3 className="text-lg font-medium text-gray-900 mb-2">{t('companies.companyNotFound')}</h3>
        <Link
          to="/companies"
          className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md inline-block"
        >
          {t('companies.backToCompanies')}
        </Link>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <Link
            to="/companies"
            className="flex items-center text-blue-600 hover:text-blue-800"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            {t('companies.backToCompanies')}
          </Link>
          <h1 className="text-2xl font-semibold mt-2">{company.name}</h1>
          <div className="flex items-center mt-1">
            <span className="text-gray-600 mr-2">{t('common.status')}:</span>
            <StatusBadge status={company.status} />
          </div>
        </div>
        {isAdmin && (
          <div className="flex space-x-3">
            <button
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
              onClick={() => setIsEditModalOpen(true)}
            >
              {t('companies.editCompany')}
            </button>
            <button
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md"
              onClick={() => setShowDeleteConfirm(true)}
            >
              {t('companies.deleteCompany')}
            </button>
          </div>
        )}
      </div>
      
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex">
            <button
              className={`py-4 px-6 ${
                activeTab === 'overview'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('overview')}
            >
              {t('companies.tabs.overview')}
            </button>
            <button
              className={`py-4 px-6 ${
                activeTab === 'vessels'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('vessels')}
            >
              {t('companies.tabs.vessels')}
            </button>
          </nav>
        </div>
        
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Company Details */}
              <div className="md:col-span-1">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-4">{t('companies.companyInformation')}</h4>

                  <div className="space-y-4 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.name')}:</span>
                      <span className="text-gray-900 font-medium">{company.name}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('companies.location')}:</span>
                      <span className="text-gray-900 font-medium">{company.location}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('companies.industry')}:</span>
                      <span className="text-gray-900 font-medium">{company.industry}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.vessels')}:</span>
                      <span className="text-gray-900 font-medium">{companyVessels.length}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.sensors')}:</span>
                      <span className="text-gray-900 font-medium">
                        {companyVessels.reduce((sum, vessel) => sum + (Array.isArray(vessel.sensors) ? vessel.sensors.length : (vessel.sensors || 0)), 0)}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('common.status')}:</span>
                      <span>
                        <StatusBadge status={company.status} />
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('vessels.lastUpdated')}:</span>
                      <span className="text-gray-900 font-medium">
                        {company.lastUpdated ? new Date(company.lastUpdated).toLocaleString('el-GR', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        }) : '-'}
                      </span>
                    </div>
                  </div>
                </div>
                
              </div>
              
              {/* Stats and Activity */}
              <div className="md:col-span-2">
                <h4 className="font-medium text-gray-900 mb-4">{t('companies.companyPerformance')}</h4>

                {/* Stats Cards */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">{t('companies.stats.activeVessels')}</div>
                    <div className="flex items-end justify-between">
                      <div className="text-2xl font-bold text-blue-600">{companyVessels.filter(v => v.status === 'Active').length}</div>
                      <div className="text-sm text-green-600">
                        {companyVessels.length > 0 ? ((companyVessels.filter(v => v.status === 'Active').length / companyVessels.length) * 100).toFixed(0) : 0}% {t('companies.stats.ofFleet')}
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">{t('companies.stats.totalVessels')}</div>
                    <div className="flex items-end justify-between">
                      <div className="text-2xl font-bold text-blue-600">{companyVessels.length}</div>
                      <div className="text-sm text-gray-500">{t('companies.stats.inFleet')}</div>
                    </div>
                  </div>
                </div>
                
                {/* Recent Activity */}
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <h5 className="font-medium text-gray-900 dark:text-white mb-4">{t('customers.recentActivity')}</h5>
                  
                  {notificationsLoading ? (
                    <div className="space-y-4">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="flex animate-pulse">
                          <div className="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
                          <div className="ml-3 flex-1">
                            <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4 mb-1"></div>
                            <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-1/4"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : companyNotifications.length > 0 ? (
                    <div className="space-y-4">
                      {companyNotifications.map((notification: any) => (
                        <div key={notification.id} className="flex">
                          <div className="flex-shrink-0">
                            {getNotificationIcon(notification.type)}
                          </div>
                          <div className="ml-3">
                            <p className="text-sm text-gray-900 dark:text-gray-300">
                              {notification.title && (
                                <span className="font-medium">{notification.title}</span>
                              )}
                              {notification.message && notification.title && ' - '}
                              {notification.message}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {formatTimestamp(notification.timestamp)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-sm text-gray-500 dark:text-gray-400">{t('notifications.noNotifications')}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'vessels' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h4 className="font-medium text-gray-900">{t('companies.companyVessels')}</h4>
                {isAdmin && (
                  <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                    {t('companies.addNewVessel')}
                  </button>
                )}
              </div>
              
              {companyVessels.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.name')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.type')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.status')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.sensors')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.location')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.lastUpdated')}
                        </th>
                        <th scope="col" className="relative px-6 py-3">
                          <span className="sr-only">{t('common.actions')}</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {companyVessels.map(vessel => (
                        <tr key={vessel.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="font-medium text-gray-900">{vessel.name}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {vessel.type}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <StatusBadge status={vessel.status} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {vessel.sensors}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {typeof vessel.location === 'string'
                              ? vessel.location
                              : vessel.location
                                ? `${vessel.location.latitude.toFixed(4)}, ${vessel.location.longitude.toFixed(4)}`
                                : vessel.homePort || t('vessels.locationUnknown')
                            }
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {vessel.lastUpdated ? new Date(vessel.lastUpdated).toLocaleString('el-GR', {
                              day: '2-digit',
                              month: '2-digit',
                              year: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            }) : '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              className="text-blue-600 hover:text-blue-900"
                              onClick={() => handleSelectVessel(vessel.id)}
                            >
                              {t('common.view')}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="bg-white p-8 text-center border rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{t('companies.noVesselsFound')}</h3>
                  <p className="text-gray-600 mb-4">
                    {t('companies.noVesselsMessage')}
                  </p>
                  {isAdmin && (
                    <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center mx-auto">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                      </svg>
                      {t('companies.addNewVessel')}
                    </button>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Edit Company Slideover */}
      <div 
        className={`fixed inset-y-0 right-0 bg-white shadow-xl transform transition-all duration-300 ease-in-out z-50 ${
          isEditModalOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{ width: `${sidebarWidth}%` }}
      >
        {/* Resize handle */}
        <div 
          className="absolute top-0 bottom-0 -left-1 w-2 cursor-col-resize"
          onMouseDown={startResize}
        >
          <div className="absolute top-1/2 transform -translate-y-1/2 -left-3 w-6 h-12 bg-blue-500 hover:bg-blue-600 rounded-l-md flex items-center justify-center cursor-col-resize group transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
            </svg>
            <span className="absolute left-0 ml-8 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Drag to resize
            </span>
          </div>
        </div>

        {/* Indicator for when dragging is active */}
        {isDragging && (
          <div className="fixed inset-0 cursor-col-resize z-40" />
        )}

        <div className="h-full">
          {isEditModalOpen && (
            <EditCompanyForm
              company={company}
              onSave={handleSaveCompany}
              onCancel={() => setIsEditModalOpen(false)}
            />
          )}
        </div>
      </div>
      
      {/* Backdrop */}
      {isEditModalOpen && (
        <div 
          className="fixed inset-0 bg-gray-600 bg-opacity-50 z-40"
          onClick={() => setIsEditModalOpen(false)}
        ></div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <>
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 z-50" onClick={() => setShowDeleteConfirm(false)}></div>
          <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 z-50 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {t('companies.confirmDelete')}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
              {t('companies.deleteWarning', { companyName: company?.name })}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={deleteCompany.isPending}
              >
                {t('common.cancel')}
              </button>
              <button
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md disabled:opacity-50"
                onClick={handleDeleteCompany}
                disabled={deleteCompany.isPending}
              >
                {deleteCompany.isPending ? t('common.deleting') : t('common.delete')}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default CompanyDetailPage;