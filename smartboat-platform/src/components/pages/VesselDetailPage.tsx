import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation, Link } from 'react-router-dom';
import { StatusBadge, SensorChart, VesselMap, LoadingOverlay, ErrorNotification } from '../common';
import { Vessel } from '../../types';
import { useAuth } from '../../context/AuthContext';
import EditVesselForm from '../features/vessels/EditVesselForm';
import { useTranslation } from 'react-i18next';
import { useVessel, useDeleteVessel } from '../../hooks/queries/useVesselQueries';

const VesselDetailPage: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const location = useLocation();

  // Keep vesselId as string for UUID support
  const vesselId = id;
  
  const [activeTab, setActiveTab] = useState('info');
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(30); // Width in percentage
  const [isDragging, setIsDragging] = useState(false); // Add state for dragging
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const deleteVessel = useDeleteVessel();
  const { hasPermission } = useAuth();

  // Fetch vessel data using React Query
  const {
    data: vessel,
    isLoading: vesselLoading,
    isError: vesselError,
    error,
    refetch
  } = useVessel(vesselId, {
    useMockFallback: false,
    staleTime: 0,
    cacheTime: 0,
    retry: false,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false
  });

  // Get sensors from the vessel data (no separate API call needed)
  const vesselSensors = vessel?.sensors || [];
  const sensorsLoading = vesselLoading;
  const sensorsError = vesselError;

  // Debug logging
  console.log('Vessel ID:', vesselId);
  console.log('Vessel Sensors:', vesselSensors);
  console.log('Sensors Loading:', sensorsLoading);
  console.log('Sensors Error:', sensorsError);
  
  // Log individual sensor details
  if (vesselSensors && vesselSensors.length > 0) {
    console.log('First sensor details:', vesselSensors[0]);
    vesselSensors.forEach((sensor, index) => {
      console.log(`Sensor ${index}:`, {
        id: sensor.id,
        name: sensor.name,
        vesselId: sensor.vesselId,
        type: sensor.type
      });
    });
  }

  // Check if the location state has editMode set to true
  useEffect(() => {
    if (location.state && location.state.editMode) {
      setIsEditModalOpen(true);
    }
  }, [location.state]);

  // Check if the user is an admin
  const isAdmin = currentUser?.role === 'Administrator' || currentUser?.role === 'Manager';

  // Handle vessel update
  const handleSaveVessel = (updatedVessel: Vessel) => {
    setIsEditModalOpen(false);
    // Refetch vessel data to get the latest from the API
    refetch();
    console.log('Vessel updated:', updatedVessel);
  };

  // Handle vessel deletion
  const handleDeleteVessel = async () => {
    try {
      await deleteVessel.mutateAsync(vesselId);
      setShowDeleteConfirm(false);
      navigate('/vessels');
    } catch (error) {
      console.error('Failed to delete vessel:', error);
      alert(t('vessels.deleteFailed'));
    }
  };

  // Improved resize handler with isDragging state
  function startResize(e: React.MouseEvent) {
    e.preventDefault();
    setIsDragging(true);

    // Define handlers inline to avoid hook issues
    const onMouseMove = (moveEvent: MouseEvent) => {
      // Calculate new width based on mouse position
      const windowWidth = window.innerWidth;
      const mouseX = moveEvent.clientX;

      // Convert to percentage (100 - x because we're dragging from right side)
      const widthPercent = 100 - (mouseX / windowWidth * 100);

      // Limit to reasonable values (between 20% and 80%)
      const newWidth = Math.max(20, Math.min(80, widthPercent));
      setSidebarWidth(newWidth);
    };

    const onMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  // Loading state - wait for both vessel and sensors
  if (vesselLoading || sensorsLoading) {
    return (
      <div className="container mx-auto relative">
        <LoadingOverlay 
          message="Loading vessel data..."
          subMessage="Fetching vessel details and sensors..."
        />
      </div>
    );
  }

  // Error state
  if (vesselError) {
    return (
      <div className="container mx-auto">
        <ErrorNotification
          message={t('common.errorFetchingData')}
          error={error}
          onRetry={refetch}
        />
      </div>
    );
  }

  // Vessel not found
  if (!vessel) {
    return (
      <div className="bg-white rounded-lg shadow p-8 text-center">
        <h3 className="text-lg font-medium text-gray-900 mb-2">{t('vessels.vesselNotFound')}</h3>
        <Link
          to="/vessels"
          className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md inline-block"
        >
          {t('vessels.backToVessels')}
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <Link
            to="/vessels"
            className="flex items-center text-blue-600 hover:text-blue-800"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            {t('vessels.backToVessels')}
          </Link>
          <h1 className="text-2xl font-semibold mt-2">{vessel.name}</h1>
          <div className="flex items-center mt-1">
            <span className="text-gray-600 mr-2">{t('common.status')}:</span>
            <StatusBadge status={vessel.status} />
          </div>
        </div>
        {isAdmin && (
          <div className="flex space-x-3">
            <button
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
              onClick={() => setIsEditModalOpen(true)}
            >
              {t('vessels.editVessel')}
            </button>
            {(hasPermission('manageCompanies') || isAdmin) && (
              <button
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md"
                onClick={() => setShowDeleteConfirm(true)}
              >
                {t('vessels.deleteVessel')}
              </button>
            )}
          </div>
        )}
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex">
            <button
              className={`py-4 px-6 ${
                activeTab === 'info'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('info')}
            >
              {t('vessels.vesselInformation')}
            </button>
            <button
              className={`py-4 px-6 ${
                activeTab === 'map'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('map')}
            >
              {t('vessels.tabs.routeMap')}
            </button>
            <button
              className={`py-4 px-6 ${
                activeTab === 'sensors'
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('sensors')}
            >
              {t('vessels.tabs.sensors')}
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'info' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Vessel Details */}
              <div className="md:col-span-1">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-4">{t('vessels.vesselInformation')}</h4>

                  <div className="space-y-4 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('vessels.vesselNumber')}:</span>
                      <span className="text-gray-900 font-medium">{vessel.number}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('vessels.type')}:</span>
                      <span className="text-gray-900 font-medium">{vessel.type}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('vessels.location')}:</span>
                      <span className="text-gray-900 font-medium">
                        {typeof vessel.location === 'string'
                          ? vessel.location
                          : vessel.location
                            ? `${vessel.location.latitude.toFixed(4)}, ${vessel.location.longitude.toFixed(4)}`
                            : vessel.homePort || t('vessels.locationUnknown')}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('vessels.startDate')}:</span>
                      <span className="text-gray-900 font-medium">
                        {vessel.startDate ? new Date(vessel.startDate).toLocaleDateString('el-GR', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric'
                        }) : '-'}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('vessels.endDate')}:</span>
                      <span className="text-gray-900 font-medium">
                        {vessel.endDate ? new Date(vessel.endDate).toLocaleDateString('el-GR', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric'
                        }) : '-'}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('vessels.totalSensors')}:</span>
                      <span className="text-gray-900 font-medium">
                        {Array.isArray(vessel.sensors) ? vessel.sensors.length : (vessel.sensors || 0)}
                      </span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-500">{t('vessels.lastUpdated')}:</span>
                      <span className="text-gray-900 font-medium">
                        {vessel.lastUpdated ? new Date(vessel.lastUpdated).toLocaleString('el-GR', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        }) : '-'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          )}

          {activeTab === 'map' && (
            <VesselMap vesselId={vessel.id} pathData={vessel.pathPoints} />
          )}

          {activeTab === 'sensors' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900">{t('vessels.vesselSensors')}</h4>
                {isAdmin && (
                  <button className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 text-sm rounded-md flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                    {t('common.add')} {t('common.sensors')}
                  </button>
                )}
              </div>

              {/* Loading state */}
              {sensorsLoading && (
                <div className="text-center py-8">
                  <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                  <p className="text-gray-500">{t('common.loading')}</p>
                </div>
              )}

              {/* Error state */}
              {sensorsError && (
                <div className="text-center py-8">
                  <p className="text-red-500 mb-4">{t('sensors.errorLoadingSensors')}</p>
                </div>
              )}

              {/* Sensors table */}
              {!sensorsLoading && !sensorsError && vesselSensors.length > 0 && (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('sensors.name')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('sensors.type')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('sensors.location')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.status')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('sensors.lastReading')}
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('common.actions')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {/* Show sensors for this vessel */}
                      {vesselSensors.map((sensor) => (
                          <tr key={sensor.id}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">{sensor.name}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-500">{sensor.type}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-500">{sensor.location}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <StatusBadge status={sensor.status} />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {sensor.lastReading}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <Link to={`/sensors/${sensor.id}`} className="text-blue-600 hover:text-blue-900">
                                {t('common.view')}
                              </Link>
                              {isAdmin && (
                                <button className="text-blue-600 hover:text-blue-900 ml-4">
                                  {t('common.edit')}
                                </button>
                              )}
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Empty state if no sensors */}
              {!sensorsLoading && !sensorsError && vesselSensors.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">{t('sensors.noSensors')}</p>
                  {isAdmin && (
                    <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                      {t('common.add')} {t('common.sensors')}
                    </button>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Edit Vessel Slideover */}
      <div
        className={`fixed inset-y-0 right-0 bg-white shadow-xl transform transition-all duration-300 ease-in-out z-50 ${
          isEditModalOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        style={{ width: `${sidebarWidth}%` }}
      >
        {/* Resize handle */}
        <div
          className="absolute top-0 bottom-0 -left-1 w-2 cursor-col-resize"
          onMouseDown={startResize}
        >
          <div className="absolute top-1/2 transform -translate-y-1/2 -left-3 w-6 h-12 bg-blue-500 hover:bg-blue-600 rounded-l-md flex items-center justify-center cursor-col-resize group transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
            </svg>
            <span className="absolute left-0 ml-8 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              {t('common.dragToResize')}
            </span>
          </div>
        </div>

        {/* Indicator for when dragging is active */}
        {isDragging && (
          <div className="fixed inset-0 cursor-col-resize z-40" />
        )}

        <div className="h-full">
          {isEditModalOpen && (
            <EditVesselForm
              vessel={vessel}
              onSave={handleSaveVessel}
              onCancel={() => setIsEditModalOpen(false)}
            />
          )}
        </div>
      </div>

      {/* Backdrop */}
      {isEditModalOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-50 z-40"
          onClick={() => setIsEditModalOpen(false)}
        ></div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <>
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 z-50" onClick={() => setShowDeleteConfirm(false)}></div>
          <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 z-50 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {t('vessels.confirmDelete')}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
              {t('vessels.deleteWarning', { vesselName: vessel?.name })}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={deleteVessel.isPending}
              >
                {t('common.cancel')}
              </button>
              <button
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md disabled:opacity-50"
                onClick={handleDeleteVessel}
                disabled={deleteVessel.isPending}
              >
                {deleteVessel.isPending ? t('common.deleting') : t('common.delete')}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default VesselDetailPage;