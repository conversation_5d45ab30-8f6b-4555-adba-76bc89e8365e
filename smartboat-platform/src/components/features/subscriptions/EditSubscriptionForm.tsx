import React, { useState, useEffect } from 'react';
import { Subscription } from '../../../types';
import { useTranslation } from 'react-i18next';
import { useCustomers } from '../../../hooks/queries/useCustomerQueries';
import { ErrorNotification, LoadingOverlay } from '../../common';

interface EditSubscriptionFormProps {
  subscription: Subscription | null; // The subscription to edit, null if creating a new one
  onSave: (subscription: Subscription) => void;
  onCancel: () => void;
  isLoading?: boolean; // Loading state for API operations
}

const EditSubscriptionForm: React.FC<EditSubscriptionFormProps> = ({
  subscription,
  onSave,
  onCancel,
  isLoading = false
}) => {
  const { t } = useTranslation();
  // Fetch customers data for dropdown
  const { 
    data: customers = [], 
    isLoading: isLoadingCustomers, 
    isError: isErrorCustomers,
    error: customersError,
    refetch: refetchCustomers
  } = useCustomers({
    useMockFallback: true // Use mock data if API fails
  });
  
  // Form errors state
  const [formError, setFormError] = useState<string | null>(null);
  
  // Get the current date in YYYY-MM-DD format for date inputs
  const getCurrentDate = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${month}/${day}/${year}`;
  };

  // Get the default end date (1 year from today)
  const getDefaultEndDate = () => {
    const today = new Date();
    const nextYear = new Date(today.setFullYear(today.getFullYear() + 1));
    const year = nextYear.getFullYear();
    const month = String(nextYear.getMonth() + 1).padStart(2, '0');
    const day = String(nextYear.getDate()).padStart(2, '0');
    return `${month}/${day}/${year}`;
  };

  // Form state
  const [formData, setFormData] = useState<Partial<Subscription>>({
    name: '',
    type: 'Standard',
    customerId: '',
    startDate: getCurrentDate(),
    endDate: getDefaultEndDate(),
    price: 100,
    billingFrequency: 'Monthly',
    status: 'Active',
    sensorLimit: 5,
    features: []
  });

  // Active section state
  const [activeSection, setActiveSection] = useState('basic');

  // Initialize form data when subscription changes
  useEffect(() => {
    if (subscription) {
      setFormData({
        id: subscription.id,
        name: subscription.name,
        type: subscription.type,
        customerId: subscription.customerId,
        customerName: subscription.customerName,
        startDate: subscription.startDate,
        endDate: subscription.endDate,
        price: subscription.price,
        billingFrequency: subscription.billingFrequency,
        status: subscription.status,
        sensorLimit: subscription.sensorLimit,
        features: subscription.features || [],
        lastUpdated: subscription.lastUpdated
      });
    } else {
      // Reset form if not editing (creating new)
      setFormData({
        name: '',
        type: 'Standard',
        customerId: '',
        startDate: getCurrentDate(),
        endDate: getDefaultEndDate(),
        price: 100,
        billingFrequency: 'Monthly',
        status: 'Active',
        sensorLimit: 5,
        features: [
          "Up to 5 sensors per vessel",
          "Daily data updates",
          "Email alerts",
          "Basic reporting"
        ]
      });
    }
  }, [subscription]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name === 'type') {
      // Update plan-specific fields based on type
      let newPrice = formData.price;
      let newSensorLimit = formData.sensorLimit;
      let newFeatures: string[] = [];

      switch (value) {
        case 'Standard':
          newPrice = 100;
          newSensorLimit = 5;
          newFeatures = [
            "Up to 5 sensors per vessel",
            "Daily data updates",
            "Email alerts",
            "Basic reporting"
          ];
          break;
        case 'Professional':
          newPrice = 150;
          newSensorLimit = 10;
          newFeatures = [
            "Up to 10 sensors per vessel",
            "Hourly data updates",
            "SMS and email alerts",
            "Advanced reporting",
            "30-day data history"
          ];
          break;
        case 'Enterprise':
          newPrice = 200;
          newSensorLimit = null; // Unlimited
          newFeatures = [
            "Unlimited sensors per vessel",
            "Real-time data updates",
            "Priority support",
            "Advanced analytics",
            "1-year data history"
          ];
          break;
        case 'Custom':
          // Keep current values for custom plans
          break;
      }

      setFormData(prev => ({
        ...prev,
        type: value,
        price: newPrice,
        sensorLimit: newSensorLimit,
        features: newFeatures
      }));
    } else if (name === 'customerId') {
      // Find customer by ID (now string-based)
      const customerId = value;
      const customer = customers.find(c => c.id === customerId);
      
      setFormData(prev => ({
        ...prev,
        customerId,
        customerName: customer ? customer.name : '',
        name: customer ? `${prev.type} Plan - ${customer.name}` : prev.name
      }));
    } else if (name === 'features') {
      // Handle features as an array from textarea (one per line)
      const featuresArray = value.split('\n').filter(f => f.trim() !== '');
      
      setFormData(prev => ({
        ...prev,
        features: featuresArray
      }));
    } else {
      // Handle other form fields
      const parsedValue = name === 'price' ? parseFloat(value) : value;
      
      setFormData(prev => ({
        ...prev,
        [name]: parsedValue
      }));
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate form data
    if (!formData.name || !formData.type || !formData.customerId || !formData.startDate) {
      // Basic validation - you could add more detailed validation here
      setFormError(t('common.fillAllRequired'));
      return;
    }

    // Create a complete subscription object by merging defaults for any missing fields
    const completeSubscription: Subscription = {
      id: formData.id || `sub-${Date.now()}-${Math.floor(Math.random() * 1000)}`, // Generate string ID for new subscriptions
      name: formData.name || '',
      type: formData.type || 'Standard',
      customerId: formData.customerId || '',
      customerName: formData.customerName || '',
      startDate: formData.startDate || getCurrentDate(),
      endDate: formData.endDate || getDefaultEndDate(),
      price: formData.price || 100,
      billingFrequency: formData.billingFrequency || 'Monthly',
      status: formData.status || 'Active',
      sensorLimit: formData.sensorLimit || 5,
      features: formData.features || [],
      lastUpdated: formData.lastUpdated || 'Just now',
    };

    // Save the subscription
    onSave(completeSubscription);
  };

  // Format features as a string for textarea
  const featuresString = formData.features ? formData.features.join('\n') : '';

  // Block types for the form sections
  const blockTypes = [
    { id: 'basic', label: t('common.basicInformation'), icon: '/assets/icons/document.svg', color: 'bg-blue-500' },
    { id: 'billing', label: t('subscriptions.billingDetails'), icon: '/assets/icons/credit-card.svg', color: 'bg-green-500' },
    { id: 'features', label: t('subscriptions.planFeatures'), icon: '/assets/icons/list.svg', color: 'bg-purple-500' },
  ];

  // Function to render block icon
  const renderBlockIcon = (blockType: string) => {
    switch (blockType) {
      case 'basic':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
      case 'billing':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
          </svg>
        );
      case 'features':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
        );
      default:
        return null;
    }
  };

  // Function to get block color
  const getBlockColor = (blockType: string) => {
    const block = blockTypes.find(b => b.id === blockType);
    return block ? block.color : 'bg-gray-500';
  };

  // Combined loading state
  const isFormLoading = isLoading || isLoadingCustomers;

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden flex flex-col h-full">
      {/* Loading state for customer data */}
      {isLoadingCustomers && <LoadingOverlay />}
      
      {/* Error notification for customer data fetching */}
      {isErrorCustomers && (
        <ErrorNotification 
          message={t('subscriptions.errorFetchingCustomers')} 
          onRetry={refetchCustomers}
          error={customersError}
        />
      )}
      
      {/* Form error notification */}
      {formError && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{formError}</p>
            </div>
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  type="button"
                  onClick={() => setFormError(null)}
                  className="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <span className="sr-only">{t('common.dismiss')}</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <div className="flex flex-col md:flex-row flex-1 overflow-hidden">
        {/* Left sidebar - Block selection */}
        <div className="w-full md:w-56 bg-gray-900 border-r border-gray-700 p-3 flex flex-col">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-white">
              {subscription ? t('subscriptions.editSubscription') : t('subscriptions.createNewSubscription')}
            </h2>
            <p className="text-xs text-gray-400 mt-1">
              {subscription ? t('subscriptions.updateSubscriptionDetails') : t('subscriptions.addNewSubscriptionInfo')}
            </p>
          </div>

          <div className="mb-4">
            <h3 className="text-xs uppercase tracking-wider text-gray-400 font-semibold mb-2">{t('common.blocks')}</h3>
            <div className="space-y-2">
              {blockTypes.map(block => (
                <button
                  key={block.id}
                  onClick={() => setActiveSection(block.id)}
                  className={`w-full flex items-center p-2 rounded-md transition-colors ${
                    activeSection === block.id
                      ? block.color
                      : 'bg-gray-800 hover:bg-gray-700'
                  } text-white`}
                >
                  <div className={`w-8 h-8 rounded-md flex items-center justify-center ${
                    activeSection === block.id ? 'bg-white bg-opacity-20' : block.color
                  } mr-2`}>
                    {renderBlockIcon(block.id)}
                  </div>
                  <span className="text-sm font-medium">{block.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 flex flex-col bg-gray-900 overflow-hidden">
          <div className="flex-1 overflow-y-auto p-6">
            <form id="subscription-form" onSubmit={handleSubmit} className="max-w-3xl mx-auto">
            <div className="mb-4 flex items-center justify-center">
              <h3 className="text-base font-medium text-white">
                {activeSection === 'basic' ? t('common.basicInformation') :
                 activeSection === 'billing' ? t('subscriptions.billingDetails') : t('subscriptions.planFeatures')}
              </h3>
              <div className={`ml-2 w-2 h-2 rounded-full ${getBlockColor(activeSection)}`}></div>
            </div>

            {/* Basic Information Section */}
            {activeSection === 'basic' && (
              <div className="bg-gray-800 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('basic')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('basic')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('common.basicInformation')}</h4>
                </div>
                <div className="p-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Subscription Name */}
                    <div className="md:col-span-2">
                      <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('subscriptions.subscriptionName')}*
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                      />
                    </div>

                    {/* Subscription Type */}
                    <div>
                      <label htmlFor="type" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('subscriptions.subscriptionType')}*
                      </label>
                      <select
                        id="type"
                        name="type"
                        value={formData.type || 'Standard'}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="Standard">{t('subscriptions.planTypes.standard')}</option>
                        <option value="Professional">{t('subscriptions.planTypes.professional')}</option>
                        <option value="Enterprise">{t('subscriptions.planTypes.enterprise')}</option>
                        <option value="Custom">{t('subscriptions.planTypes.custom')}</option>
                      </select>
                    </div>

                    {/* Customer */}
                    <div>
                      <label htmlFor="customerId" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('common.customers')}*
                      </label>
                      <select
                        id="customerId"
                        name="customerId"
                        value={formData.customerId || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                        disabled={isFormLoading}
                      >
                        <option value="">{isLoadingCustomers ? t('common.loading') : t('subscriptions.selectCustomer')}</option>
                        {customers.map(customer => (
                          <option key={customer.id} value={customer.id}>
                            {customer.name}
                          </option>
                        ))}
                      </select>
                      {isLoadingCustomers && (
                        <div className="mt-2 flex items-center text-sm text-gray-400">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {t('common.loadingCustomers')}
                        </div>
                      )}
                    </div>

                    {/* Status */}
                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('common.status')}
                      </label>
                      <select
                        id="status"
                        name="status"
                        value={formData.status || 'Active'}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="Active">{t('subscriptions.status.active')}</option>
                        <option value="Pending">{t('subscriptions.status.pending')}</option>
                        <option value="Expired">{t('subscriptions.status.expired')}</option>
                        <option value="Canceled">{t('subscriptions.status.cancelled')}</option>
                      </select>
                    </div>

                    {/* Start Date */}
                    <div>
                      <label htmlFor="startDate" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('subscriptions.startDate')}*
                      </label>
                      <input
                        type="text"
                        id="startDate"
                        name="startDate"
                        value={formData.startDate || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="MM/DD/YYYY"
                        required
                      />
                    </div>

                    {/* End Date */}
                    <div>
                      <label htmlFor="endDate" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('subscriptions.endDate')}
                      </label>
                      <input
                        type="text"
                        id="endDate"
                        name="endDate"
                        value={formData.endDate || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="MM/DD/YYYY"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Billing Details Section */}
            {activeSection === 'billing' && (
              <div className="bg-gray-800 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('billing')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('billing')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('subscriptions.billingDetails')}</h4>
                </div>
                <div className="p-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Price */}
                    <div>
                      <label htmlFor="price" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('subscriptions.pricePerSensor')}*
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <span className="text-gray-400 sm:text-sm">$</span>
                        </div>
                        <input
                          type="number"
                          id="price"
                          name="price"
                          value={formData.price || ''}
                          onChange={handleChange}
                          className="block w-full pl-8 px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          required
                          step="0.01"
                          min="0"
                        />
                      </div>
                    </div>

                    {/* Billing Frequency */}
                    <div>
                      <label htmlFor="billingFrequency" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('subscriptions.billingFrequency')}
                      </label>
                      <select
                        id="billingFrequency"
                        name="billingFrequency"
                        value={formData.billingFrequency || 'Monthly'}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="Monthly">{t('subscriptions.monthly')}</option>
                        <option value="Quarterly">{t('subscriptions.quarterly')}</option>
                        <option value="Annually">{t('subscriptions.yearly')}</option>
                      </select>
                    </div>

                    {/* Sensor Limit */}
                    <div>
                      <label htmlFor="sensorLimit" className="block text-sm font-medium text-gray-300 mb-1">
                        {t('subscriptions.sensorLimit')}
                      </label>
                      <input
                        type="number"
                        id="sensorLimit"
                        name="sensorLimit"
                        value={formData.sensorLimit === null ? '' : formData.sensorLimit || ''}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="Leave empty for unlimited"
                        min="0"
                      />
                      <p className="mt-1 text-xs text-gray-400">
                        {t('subscriptions.unlimitedSensorsInfo')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Features Section */}
            {activeSection === 'features' && (
              <div className="bg-gray-800 rounded-lg border border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="p-4 border-b border-gray-700 flex items-center">
                  <div className={`w-8 h-8 ${getBlockColor('features')} rounded-md flex items-center justify-center text-white mr-3`}>
                    {renderBlockIcon('features')}
                  </div>
                  <h4 className="text-md font-medium text-gray-200">{t('subscriptions.planFeatures')}</h4>
                </div>
                <div className="p-5">
                  <div>
                    <label htmlFor="features" className="block text-sm font-medium text-gray-300 mb-1">
                      {t('subscriptions.featuresPerLine')}
                    </label>
                    <textarea
                      id="features"
                      name="features"
                      value={featuresString}
                      onChange={handleChange}
                      className="block w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      rows={8}
                      placeholder={t('subscriptions.enterFeaturesPlaceholder')}
                    />
                    <p className="mt-1 text-xs text-gray-400">
                      {t('subscriptions.featuresInstructionText')}
                    </p>
                  </div>

                  {/* Preview features as bullet list */}
                  {formData.features && formData.features.length > 0 && (
                    <div className="mt-4">
                      <h5 className="text-sm font-medium text-gray-300 mb-2">{t('common.preview')}:</h5>
                      <ul className="list-disc pl-5 space-y-1 text-gray-300">
                        {formData.features.map((feature, index) => (
                          <li key={index}>{feature}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            )}
          </form>
          </div>

          {/* Fixed footer with buttons */}
          <div className="border-t border-gray-700 p-3 bg-gray-900">
            <div className="flex flex-row space-x-3 justify-end max-w-3xl mx-auto">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-800 hover:bg-gray-700"
                disabled={isFormLoading}
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                form="subscription-form"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 flex items-center"
                disabled={isFormLoading}
              >
                {isFormLoading && (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                {subscription ? t('subscriptions.updateSubscription') : t('subscriptions.createSubscription')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditSubscriptionForm;