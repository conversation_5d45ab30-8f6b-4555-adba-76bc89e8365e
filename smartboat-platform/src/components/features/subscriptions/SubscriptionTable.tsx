import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Subscription } from '../../../types';
import { User } from '../../../types';
import { hasPermission } from '../../../utils/authUtils';
import { useTranslation } from 'react-i18next';
import { formatDateRange } from '../../../utils/dateUtils';

interface SubscriptionTableProps {
  subscriptions: Subscription[];
  currentUser: User | null;
  onView: (subscription: Subscription) => void;
  onEdit: (subscription: Subscription) => void;
}

const SubscriptionTable: React.FC<SubscriptionTableProps> = ({
  subscriptions,
  currentUser,
  onView,
  onEdit
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  // Skip rendering if no subscriptions
  if (subscriptions.length === 0) {
    return null;
  }

  // For customers, don't show the customer column
  const showCustomerColumn = currentUser?.role !== 'Customer';

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {t('common.name')}
            </th>
            {showCustomerColumn && (
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t('common.customers')}
              </th>
            )}
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {t('common.type')}
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {t('common.status')}
            </th>
            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              {t('subscriptions.price')}
            </th>
            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              {t('common.actions')}
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {subscriptions.map((subscription) => (
            <tr key={subscription.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900">{subscription.name}</div>
                <div className="text-sm text-gray-500">{formatDateRange(subscription.startDate, subscription.endDate)}</div>
              </td>
              {showCustomerColumn && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{subscription.customerName}</div>
                </td>
              )}
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">{subscription.type}</div>
                <div className="text-sm text-gray-500">{subscription.billingFrequency}</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                  ${subscription.status === 'Active' ? 'bg-green-100 text-green-800' : 
                  subscription.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 
                  subscription.status === 'Expired' ? 'bg-red-100 text-red-800' : 
                  'bg-gray-100 text-gray-800'}`}>
                  {subscription.status}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                ${subscription.price}/{subscription.billingFrequency === 'Monthly' ? 'mo' : subscription.billingFrequency === 'Quarterly' ? 'qtr' : 'yr'}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                  className="text-blue-600 hover:text-blue-900 mr-3"
                  onClick={() => navigate(`/subscriptions/${subscription.id}`)}
                >
                  {t('common.view')}
                </button>

                {/* Show Edit button based on permissions */}
                {(currentUser?.role === 'Administrator' ||
                  (currentUser?.role === 'Customer' &&
                  hasPermission(currentUser, 'changeOwnSubscription') &&
                  (subscription.userIds?.includes(currentUser.id) ||
                  subscription.customerId === currentUser.customerId))
                ) && (
                  <button
                    className="text-blue-600 hover:text-blue-900"
                    onClick={() => onEdit(subscription)}
                  >
                    {t('common.edit')}
                  </button>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default SubscriptionTable;