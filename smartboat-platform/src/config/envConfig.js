/**
 * Environment configuration for the SmartBoat Platform
 * This file centralizes all environment-specific settings
 */

// API base URL - default to development environment
// In production, if VITE_API_BASE_URL is empty, we use relative URLs
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL !== undefined 
  ? import.meta.env.VITE_API_BASE_URL 
  : 'https://localhost:7001';

// API version
export const API_VERSION = import.meta.env.VITE_API_VERSION || '';

// Request timeout in milliseconds
export const REQUEST_TIMEOUT = 30000;

// Environment name
export const ENV_NAME = import.meta.env.MODE;

// Check if we're in production
export const IS_PRODUCTION = import.meta.env.PROD || import.meta.env.MODE === 'production';

// Build version
export const BUILD_VERSION = import.meta.env.VITE_BUILD_VERSION || 'development';

// Application ID for API requests
export const APPLICATION_ID = import.meta.env.VITE_APPLICATION_ID || 'smartboat-platform';

// Feature flags
export const FEATURES = {
  // Enable/disable real-time updates
  ENABLE_REALTIME: import.meta.env.VITE_ENABLE_REALTIME === 'true',
  // Use mock data as fallback when API calls fail (default true in development)
  USE_MOCK_FALLBACK: import.meta.env.VITE_USE_MOCK_FALLBACK === 'true' || import.meta.env.MODE === 'development',
  // Always use mock data in development mode (no API calls)
  USE_MOCK_DATA: import.meta.env.VITE_USE_MOCK_DATA === 'true',
};

// Get the appropriate API base URL based on environment
export const getApiBaseUrl = () => {
  // In production with empty VITE_API_BASE_URL, use the current origin
  if (IS_PRODUCTION && API_BASE_URL === '') {
    return window.location.origin;
  }
  return API_BASE_URL;
};

// Construct the full API URL
export const getApiUrl = (endpoint) => {
  const baseUrl = getApiBaseUrl();
  return `${baseUrl}${API_VERSION ? `/${API_VERSION}` : ''}${endpoint}`;
};