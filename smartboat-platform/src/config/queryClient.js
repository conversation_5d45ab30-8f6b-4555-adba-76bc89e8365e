/**
 * React Query Client Configuration
 * Sets up React Query with custom configuration for API data fetching
 */

import { QueryClient } from '@tanstack/react-query';
import { FEATURES } from './envConfig';

// Default cache time - 5 minutes
const DEFAULT_STALE_TIME = 5 * 60 * 1000;

// Default query configuration
const defaultQueryOptions = {
  staleTime: DEFAULT_STALE_TIME,
  cacheTime: DEFAULT_STALE_TIME * 2,
  refetchOnWindowFocus: import.meta.env.PROD, // Only refetch on window focus in production
  retry: 2, // Retry failed queries twice
};

// Create Query Client
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: defaultQueryOptions,
    mutations: {
      retry: 1, // Retry failed mutations once
    },
  },
});

// Query key factory to ensure consistent query keys
export const queryKeys = {
  // Auth related keys
  auth: {
    all: ['auth'],
    currentUser: () => [...queryKeys.auth.all, 'currentUser'],
    profile: (userId) => [...queryKeys.auth.all, 'profile', userId],
  },
  
  // Vessel related keys
  vessels: {
    all: ['vessels'],
    lists: () => [...queryKeys.vessels.all, 'list'],
    detail: (id) => [...queryKeys.vessels.all, 'detail', id],
    byCompany: (companyId) => [...queryKeys.vessels.all, 'byCompany', companyId],
    path: (id, params) => [...queryKeys.vessels.all, 'path', id, params],
  },
  
  // Sensor related keys
  sensors: {
    all: ['sensors'],
    lists: () => [...queryKeys.sensors.all, 'list'],
    detail: (id) => [...queryKeys.sensors.all, 'detail', id],
    byVessel: (vesselId) => [...queryKeys.sensors.all, 'byVessel', vesselId],
    data: (id, params) => [...queryKeys.sensors.all, 'data', id, params],
    alerts: (id, params) => [...queryKeys.sensors.all, 'alerts', id, params],
  },
  
  // Company related keys
  companies: {
    all: ['companies'],
    lists: () => [...queryKeys.companies.all, 'list'],
    detail: (id) => [...queryKeys.companies.all, 'detail', id],
    vessels: (id) => [...queryKeys.companies.all, 'vessels', id],
    customers: (id) => [...queryKeys.companies.all, 'customers', id],
    subscriptions: (id) => [...queryKeys.companies.all, 'subscriptions', id],
  },
  
  // Customer related keys
  customers: {
    all: ['customers'],
    lists: () => [...queryKeys.customers.all, 'list'],
    detail: (id) => [...queryKeys.customers.all, 'detail', id],
    subscriptions: (id) => [...queryKeys.customers.all, 'subscriptions', id],
    vessels: (id) => [...queryKeys.customers.all, 'vessels', id],
  },
  
  // Subscription related keys
  subscriptions: {
    all: ['subscriptions'],
    lists: () => [...queryKeys.subscriptions.all, 'list'],
    detail: (id) => [...queryKeys.subscriptions.all, 'detail', id],
    byCustomer: (customerId) => [...queryKeys.subscriptions.all, 'byCustomer', customerId],
    plans: () => [...queryKeys.subscriptions.all, 'plans'],
  },
  
  // User related keys
  users: {
    all: ['users'],
    lists: () => [...queryKeys.users.all, 'list'],
    detail: (id) => [...queryKeys.users.all, 'detail', id],
    notifications: (id) => [...queryKeys.users.all, 'notifications', id],
  },
  
  // Notification related keys
  notifications: {
    all: ['notifications'],
    lists: (params = {}) => [...queryKeys.notifications.all, 'list', params],
    detail: (id) => [...queryKeys.notifications.all, 'detail', id],
    stats: () => [...queryKeys.notifications.all, 'stats'],
    preferences: () => [...queryKeys.notifications.all, 'preferences'],
  },
};

/**
 * Invalidate queries by type and optional ID
 */
export const invalidateQueries = (entityType, id = null) => {
  if (!queryKeys[entityType]) {
    console.warn(`Unknown entity type for cache invalidation: ${entityType}`);
    return;
  }

  if (id) {
    // Invalidate specific entity
    queryClient.invalidateQueries(queryKeys[entityType].detail(id));
  } else {
    // Invalidate all entities of this type
    queryClient.invalidateQueries(queryKeys[entityType].all);
  }
};

// Remove the function causing the import issue