# SmartBoat Database Cleanup & Setup Guide

This guide explains how to clean and set up your SmartBoat database from scratch.

## Overview

The SmartBoat platform provides comprehensive database cleanup and setup scripts for both local development (Docker) and production (Azure SQL) environments.

## Local Development (Docker)

### Prerequisites
- Docker installed and running
- SQL Server container running on port 1433

### Start SQL Server Container
```bash
docker run -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=YourStrong@Passw0rd" \
  -p 1433:1433 --name sqlserver -d mcr.microsoft.com/mssql/server:2022-latest
```

### Clean Setup Options

#### Option 1: Clean and Setup in One Command
```bash
cd SmartBoat.API
./Database/setup-database.sh --clean
```

#### Option 2: Manual Cleanup Then Setup
```bash
cd SmartBoat.API
./Database/cleanup-database.sh
./Database/setup-database.sh
```

#### Option 3: Custom Config File
```bash
cd SmartBoat.API
./Database/setup-database.sh appsettings.Production.json --clean
```

## Azure SQL Database

### Prerequisites
- SQL Server command-line tools (`sqlcmd`) installed
- Azure SQL Database created
- Admin credentials

### Install sqlcmd

**macOS:**
```bash
brew install mssql-tools
```

**Linux:**
```bash
curl https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -
curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list | sudo tee /etc/apt/sources.list.d/msprod.list
sudo apt-get update && sudo apt-get install -y mssql-tools
```

### Clean Setup Options

#### Option 1: Clean and Setup in One Command
```bash
./setup-database-manually.sh --clean
```

#### Option 2: Manual Cleanup Then Setup
```bash
./cleanup-azure-database.sh
./setup-database-manually.sh
```

## What Gets Cleaned Up

The cleanup scripts remove all SmartBoat database tables in the correct dependency order:

### Junction/Relationship Tables
- `UserRolePermissions`
- `RolePermission`
- `EntityPermissions`
- `SubscriptionFeature`
- `NotificationPreference`
- `AlertDelivery`

### Dependent Entity Tables
- `VesselPathPoints`
- `SensorDataPoints`
- `Sensors`
- `Alert`
- `Notification`
- `AuditLog`
- `Feedback`
- `SupportRequests`
- `Reports`

### Main Entity Tables
- `Vessels`
- `Subscription`
- `Customer`
- `Users`
- `Company`

### Authorization Tables
- `Permissions`
- `Role`

## What Gets Set Up

After cleanup, the setup scripts create:

1. **Authorization Module**
   - Core permission and role tables
   - Basic authorization permissions

2. **Core Tables**
   - All business entity tables (Companies, Users, Vessels, etc.)
   - Relationship and junction tables

3. **Seed Data**
   - Default users and roles
   - Sample vessels and sensors
   - Permission assignments

## Safety Features

- **Confirmation prompts** for destructive operations
- **Dependency-aware cleanup** to avoid constraint violations
- **Error checking** with detailed feedback
- **Transaction safety** where possible

## Troubleshooting

### Docker Issues
```bash
# Check if container is running
docker ps

# Restart SQL Server container
docker restart sqlserver

# Check container logs
docker logs sqlserver
```

### Azure SQL Issues
```bash
# Test connection
sqlcmd -S your-server.database.windows.net -d your-database -U admin -P password -Q "SELECT 1"

# Check for remaining tables
sqlcmd -S your-server.database.windows.net -d your-database -U admin -P password \
  -Q "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"
```

### Common Connection String Format
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=127.0.0.1,1433;Database=Smartboat;User ID=SA;Password=YourStrong@Passw0rd;TrustServerCertificate=False;Encrypt=False;Connection Timeout=30;Integrated Security=False;"
  }
}
```

## Files Created/Modified

- `SmartBoat.API/Database/cleanup-database.sh` - Local database cleanup
- `SmartBoat.API/Database/setup-database.sh` - Enhanced with --clean option
- `cleanup-azure-database.sh` - Azure SQL cleanup script
- `setup-database-manually.sh` - Enhanced with --clean option

## Quick Reference

| Command | Purpose |
|---------|---------|
| `./setup-database.sh --clean` | Clean local Docker setup |
| `./cleanup-database.sh` | Clean local Docker only |
| `./setup-database-manually.sh --clean` | Clean Azure SQL setup |
| `./cleanup-azure-database.sh` | Clean Azure SQL only |