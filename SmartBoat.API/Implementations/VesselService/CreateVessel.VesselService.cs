using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nbg.NetCore.DatabaseService;
using SmartBoat.API.Types;
using SmartBoat.API.Interfaces;
using PPG.Auth.Types;
using SmartBoat.API.ControllersExceptions;

namespace SmartBoat.API.Services
{
    public partial class VesselService
    {
        public async Task<string> Create(CreateVesselDto request, Guid userId)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.Name) || string.IsNullOrWhiteSpace(request.Number) || string.IsNullOrWhiteSpace(request.Type) || string.IsNullOrWhiteSpace(request.Location) || request.CompanyId == null)
            {
                _logger.LogInformation($"DP-422: Client Error. Invalid request parameters. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            Vessel vessel = new Vessel();

            var isAuthorizedDto = new IsAuthorizedDto<Vessel>
            {
                UserId = userId,
                PermissionToCheck = new PermissionToCheck { OperationActionId = OperationAction.Create },
                Entity = vessel
            };
            await _authorizationService.IsAuthorized(isAuthorizedDto);

            var companyRequest = new CompanyRequestDto { Id = request.CompanyId };
            var company = await _companyService.Get(companyRequest, userId);
            if (company == null)
            {
                _logger.LogInformation($"DP-404: Technical Error. Company not found with Id: {request.CompanyId}. UserId: {userId}");
                throw new TechnicalException("DP-404", "Technical Error");
            }

            var existingVessel = await _databaseService.SelectAsync<Vessel>(new { Number = request.Number });
            if (existingVessel != null && existingVessel.Any())
            {
                _logger.LogInformation($"DP-422: Client Error. Vessel with Number: {request.Number} already exists. UserId: {userId}");
                throw new BusinessException("DP-422", "Client Error");
            }

            vessel = new Vessel
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                Number = request.Number,
                Type = request.Type,
                Location = request.Location,
                Status = request.Status,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                Image = request.Image,
                Onsigners = request.Onsigners,
                Offsigners = request.Offsigners,
                CompanyId = request.CompanyId,
                LastUpdated = DateTime.Now,
                Created = DateTime.Now,
                Changed = null
            };

            try
            {
                await _databaseService.InsertAsync<Vessel>(vessel);
                var retrievedVessel = await _databaseService.SelectByIdAsync<Vessel, Guid>(vessel.Id.Value);
                if (retrievedVessel == null)
                {
                    _logger.LogInformation($"DP-404: Technical Error. Vessel not found with Id: {vessel.Id}. UserId: {userId}");
                    throw new TechnicalException("DP-404", "Technical Error");
                }
                
                // Send notifications for vessel creation
                await _entityNotificationService.NotifyEntityCreated("Vessel", vessel.Id.Value, vessel.Name, vessel.CompanyId, userId);
                
                return retrievedVessel.Id.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DP-500: Technical Error. Error while creating Vessel. UserId: {userId}");
                throw new TechnicalException("DP-500", "Technical Error");
            }
        }
    }
}