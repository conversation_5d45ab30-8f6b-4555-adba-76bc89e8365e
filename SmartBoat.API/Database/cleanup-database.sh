#!/bin/bash

# Bash script to clean up Smartboat database using Docker
# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
API_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

CONFIG_FILE="${1:-appsettings.Development.json}"

# Build full path to config file if needed
if [[ "$CONFIG_FILE" != /* ]]; then
    # If it's a relative path, make it relative to the API directory
    FULL_CONFIG_PATH="$API_DIR/$CONFIG_FILE"
else
    # If it's already absolute, use as-is
    FULL_CONFIG_PATH="$CONFIG_FILE"
fi

# Check if config file exists
if [ ! -f "$FULL_CONFIG_PATH" ]; then
    echo "❌ Configuration file not found: $FULL_CONFIG_PATH"
    echo ""
    echo "Available config files in $API_DIR:"
    ls -1 "$API_DIR"/appsettings*.json 2>/dev/null || echo "  No appsettings*.json files found"
    exit 1
fi

# Parse connection string without jq (using sed/grep)
CONNECTION_STRING=$(grep -o '"DefaultConnection"[[:space:]]*:[[:space:]]*"[^"]*"' "$FULL_CONFIG_PATH" | sed 's/.*"DefaultConnection"[[:space:]]*:[[:space:]]*"//' | sed 's/".*//')

# Parse connection string components
SERVER=$(echo "$CONNECTION_STRING" | grep -o 'Server=[^;]*' | cut -d'=' -f2)
USER_ID=$(echo "$CONNECTION_STRING" | grep -o 'User ID=[^;]*' | cut -d'=' -f2)
PASSWORD=$(echo "$CONNECTION_STRING" | grep -o 'Password=[^;]*' | cut -d'=' -f2)

echo "=== SmartBoat Database Cleanup ==="
echo "Connecting to server: $SERVER"
echo "Using User ID: $USER_ID"
echo ""

# Function to execute SQL with error checking
execute_sql() {
    local sql_command="$1"
    local description="$2"
    
    echo "⚠️  $description..."
    docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U "$USER_ID" -P "$PASSWORD" -C -d Smartboat -Q "$sql_command"
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed: $description"
        return 1
    else
        echo "✅ Success: $description"
        return 0
    fi
}

# Check if database exists
DB_EXISTS=$(docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U "$USER_ID" -P "$PASSWORD" -C -Q "SELECT name FROM sys.databases WHERE name = 'Smartboat'" | grep -c "Smartboat")

if [ "$DB_EXISTS" -eq 0 ]; then
    echo "ℹ️  Database 'Smartboat' does not exist. Nothing to clean up."
    exit 0
fi

echo "🗑️  Starting database cleanup..."
echo ""

# Disable foreign key constraints temporarily
execute_sql "EXEC sp_MSforeachtable 'ALTER TABLE ? NOCHECK CONSTRAINT ALL'" "Disabling foreign key constraints"

# Drop tables in reverse dependency order to avoid constraint violations
echo ""
echo "🔥 Dropping tables..."

# Drop junction/relationship tables first
execute_sql "IF OBJECT_ID('UserRolePermissions', 'U') IS NOT NULL DROP TABLE UserRolePermissions;" "Dropping UserRolePermissions"
execute_sql "IF OBJECT_ID('RolePermission', 'U') IS NOT NULL DROP TABLE RolePermission;" "Dropping RolePermission"
execute_sql "IF OBJECT_ID('EntityPermissions', 'U') IS NOT NULL DROP TABLE EntityPermissions;" "Dropping EntityPermissions"
execute_sql "IF OBJECT_ID('SubscriptionFeature', 'U') IS NOT NULL DROP TABLE SubscriptionFeature;" "Dropping SubscriptionFeature"
execute_sql "IF OBJECT_ID('NotificationPreference', 'U') IS NOT NULL DROP TABLE NotificationPreference;" "Dropping NotificationPreference"
execute_sql "IF OBJECT_ID('AlertDelivery', 'U') IS NOT NULL DROP TABLE AlertDelivery;" "Dropping AlertDelivery"

# Drop dependent entity tables
execute_sql "IF OBJECT_ID('VesselPathPoints', 'U') IS NOT NULL DROP TABLE VesselPathPoints;" "Dropping VesselPathPoints"
execute_sql "IF OBJECT_ID('SensorDataPoints', 'U') IS NOT NULL DROP TABLE SensorDataPoints;" "Dropping SensorDataPoints"
execute_sql "IF OBJECT_ID('Sensors', 'U') IS NOT NULL DROP TABLE Sensors;" "Dropping Sensors"
execute_sql "IF OBJECT_ID('Alert', 'U') IS NOT NULL DROP TABLE Alert;" "Dropping Alert"
execute_sql "IF OBJECT_ID('Notification', 'U') IS NOT NULL DROP TABLE Notification;" "Dropping Notification"
execute_sql "IF OBJECT_ID('AuditLog', 'U') IS NOT NULL DROP TABLE AuditLog;" "Dropping AuditLog"
execute_sql "IF OBJECT_ID('Feedback', 'U') IS NOT NULL DROP TABLE Feedback;" "Dropping Feedback"
execute_sql "IF OBJECT_ID('SupportRequests', 'U') IS NOT NULL DROP TABLE SupportRequests;" "Dropping SupportRequests"
execute_sql "IF OBJECT_ID('Reports', 'U') IS NOT NULL DROP TABLE Reports;" "Dropping Reports"

# Drop main entity tables
execute_sql "IF OBJECT_ID('Vessels', 'U') IS NOT NULL DROP TABLE Vessels;" "Dropping Vessels"
execute_sql "IF OBJECT_ID('Subscription', 'U') IS NOT NULL DROP TABLE Subscription;" "Dropping Subscription"
execute_sql "IF OBJECT_ID('Customer', 'U') IS NOT NULL DROP TABLE Customer;" "Dropping Customer"
execute_sql "IF OBJECT_ID('Users', 'U') IS NOT NULL DROP TABLE Users;" "Dropping Users"
execute_sql "IF OBJECT_ID('Company', 'U') IS NOT NULL DROP TABLE Company;" "Dropping Company"

# Drop authorization tables
execute_sql "IF OBJECT_ID('Permissions', 'U') IS NOT NULL DROP TABLE Permissions;" "Dropping Permissions"
execute_sql "IF OBJECT_ID('Role', 'U') IS NOT NULL DROP TABLE Role;" "Dropping Role"

echo ""
echo "🔄 Checking remaining tables..."

# List any remaining tables (filter out system messages)
REMAINING_TABLES=$(docker exec -i sqlserver /opt/mssql-tools18/bin/sqlcmd -S localhost -U "$USER_ID" -P "$PASSWORD" -C -d Smartboat -Q "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'" -h-1 | tr -d ' \r' | grep -v '^$' | grep -v 'affected' | grep -v '^(')

if [ -n "$REMAINING_TABLES" ]; then
    echo "⚠️  Warning: Some tables still exist:"
    echo "$REMAINING_TABLES"
    echo ""
    echo "🧹 Attempting to drop remaining tables..."
    
    # Try to drop any remaining tables (only valid table names)
    for table in $REMAINING_TABLES; do
        if [ -n "$table" ] && [[ "$table" =~ ^[A-Za-z][A-Za-z0-9_]*$ ]]; then
            execute_sql "DROP TABLE [$table];" "Dropping remaining table: $table" || true
        fi
    done
fi

echo ""
echo "✨ Database cleanup completed!"
echo ""
echo "📋 Summary:"
echo "- All tables have been dropped"
echo "- Database 'Smartboat' is now empty"
echo "- Ready for fresh setup"
echo ""
echo "💡 Next steps:"
echo "   Run: ./Database/setup-database.sh"
echo "   Or:  ./Database/setup-database.sh --clean (includes cleanup)"