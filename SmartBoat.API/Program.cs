using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Data.SqlClient;
using System.Data;
using SmartBoat.API.Interfaces;
using SmartBoat.API.Services;
using SmartBoat.API.Implementations.AlertService;
using SmartBoat.API.Implementations;
using PPG.Auth.Interfaces;
using PPG.Auth.Implementations;
using Nbg.NetCore.DatabaseService;
using Nbg.NetCore.AutocodeDbOperations;
using PPG.Auth.Extensions;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using SmartBoat.API.Middleware;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add JWT Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        var jwtSettings = builder.Configuration.GetSection("Jwt");
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings["SecretKey"]!)),
            ValidateIssuer = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidateAudience = true,
            ValidAudience = jwtSettings["Audience"],
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero
        };
    });

builder.Services.AddAuthorization();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        var corsConfig = builder.Configuration.GetSection("Cors");
        var allowedOrigins = corsConfig.GetSection("AllowedOrigins").Get<string[]>() ?? new[] { "http://localhost:5173" };

        policy.WithOrigins(allowedOrigins)
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

// Memory Cache
builder.Services.AddMemoryCache();

// Database Connection
builder.Services.AddScoped<IDbConnection>(provider =>
{
    var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
    var conn = new SqlConnection(connectionString);
    conn.Open();
    return conn;
});
builder.Services.AddScoped<IAutoCodeDbOperationsService, AutoCodeDbOperationsService>();
builder.Services.AddScoped<IDatabaseService>(provider =>
{
    var autoCodeService = provider.GetRequiredService<IAutoCodeDbOperationsService>();
    return autoCodeService.GetDatabaseService();
});


builder.Services.AddDatabaseService();


builder.Services.AddAuthorizationScopedImplementations();
builder.Services.AddMyPackageControllers();

// Database Connection Factory for Authorization Module
builder.Services.AddSingleton<PPG.Auth.Interfaces.IDbConnectionFactory>(sp =>
{
    var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
    return new PPG.Auth.Implementations.DbConnectionFactory(connectionString);
});

// Authorization Provider Services (bridge main app to authorization module)
builder.Services.AddScoped<IUserProvider, UserProvider>();
builder.Services.AddScoped<IRoleProvider, RoleProvider>();
builder.Services.AddScoped<IAuthorizationConfigProvider, AuthorizationConfigProvider>();

// Authorization Services (registered via extension method)
// Note: Providers are registered above, core authorization services below

// Core Services
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<ICompanyService, CompanyService>();
builder.Services.AddScoped<ICustomerService, CustomerService>();
builder.Services.AddScoped<ISubscriptionService, SubscriptionService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<IVesselService, VesselService>();

// Factory to break circular dependency between Sensor and Vessel services
builder.Services.AddScoped<IVesselServiceFactory, VesselServiceFactory>();
builder.Services.AddScoped<ISensorService, SensorService>();
builder.Services.AddScoped<ISensorDataPointService, SensorDataPointService>();
builder.Services.AddScoped<IAuditLogService, AuditLogService>();
builder.Services.AddScoped<IAlertService, AlertService>();

// Notification Services
builder.Services.AddScoped<INotificationPreferenceService, NotificationPreferenceService>();
builder.Services.AddScoped<INotificationService, NotificationService>();
builder.Services.AddScoped<IEntityNotificationService, EntityNotificationService>();

// JWT and Auth Services
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<IAuthService, AuthService>();

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("AllowFrontend");
app.UseHttpsRedirection();
app.UseAuthentication();
app.UseMiddleware<JwtUserMiddleware>();
app.UseAuthorization();
app.MapControllers();

app.Run();