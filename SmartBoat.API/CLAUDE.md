# SmartBoat API - Claude Development Guide

This file contains information for <PERSON> to understand the project structure and common tasks.

## Project Overview
- **Technology**: ASP.NET Core Web API (.NET 8)
- **Database**: SQL Server (via Docker)
- **Architecture**: Service-based architecture with repositories

## Database
- **Connection String**: Located in `appsettings.Development.json`
- **Setup Script**: `./Database/setup-database.sh` (uses Docker sqlcmd)
- **Tables**: All SQL files are in the `Database/` folder

## Running the Project
```bash
# Start SQL Server
docker run -e "ACCEPT_EULA=Y" -e "MSSQL_SA_PASSWORD=YourStrong@Passw0rd" -p 1433:1433 --name sqlserver -d mcr.microsoft.com/mssql/server:2022-latest

# Setup database
./Database/setup-database.sh

# Run application
dotnet run
```

## Project Structure
- `Controllers/` - API controllers
- `Implementations/` - Service implementations (grouped by service)
- `Interfaces/` - Service interfaces
- `Types/` - DTOs and models
- `Database/` - SQL table definitions
- `Authorization/` - Permission and authorization system
- `Notification/` - Notification system

## Common Commands
- **Build**: `dotnet build`
- **Run**: `dotnet run`
- **Test**: No test framework configured yet

## Development Notes
- Environment automatically set to Development via `Properties/launchSettings.json`
- Swagger available at https://localhost:7001/swagger in Development
- Database setup script uses Docker to avoid requiring local SQL tools