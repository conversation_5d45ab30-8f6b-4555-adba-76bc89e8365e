// File: UpdateVesselDto.cs
using System;

namespace SmartBoat.API.Types
{
    public class UpdateVesselDto
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public string? Number { get; set; }
        public string? Type { get; set; }
        public string? Location { get; set; }
        public string? Status { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Image { get; set; }
        public int? Onsigners { get; set; }
        public int? Offsigners { get; set; }
        public Guid? CompanyId { get; set; }
    }
}