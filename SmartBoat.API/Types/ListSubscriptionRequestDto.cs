// File: ListSubscriptionRequestDto.cs
using System;

namespace SmartBoat.API.Types
{
    public class ListSubscriptionRequestDto
    {
        public int? PageLimit { get; set; }
        public int? PageOffset { get; set; }
        public string? SortField { get; set; }
        public string? SortOrder { get; set; }
        public string? SearchTerm { get; set; }
        public string? Name { get; set; }
        public Guid? CustomerId { get; set; }
        public string? Status { get; set; }
    }
}