// File: Vessel.cs
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartBoat.API.Types
{
    [Table("Vessels")]
    public class Vessel
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public string? Number { get; set; }
        public string? Type { get; set; }
        public string? Location { get; set; }
        public string? Status { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Image { get; set; }
        public int? Onsigners { get; set; }
        public int? Offsigners { get; set; }
        public Guid? CompanyId { get; set; }
        public DateTime? LastUpdated { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Changed { get; set; }
    }
}